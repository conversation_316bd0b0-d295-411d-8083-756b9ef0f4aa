/**
 * 详细日志测试脚本
 * 展示并行任务路由器的每一步执行过程
 */

// 设置详细日志级别
process.env.LOG_LEVEL = 'DEBUG';

const { ParallelTaskRouter } = require('./parallel_task_router.js');
const router = require('./my_custom_router.js');

/**
 * 测试用例
 */
const testCases = [
  {
    name: "简单代码任务",
    description: "测试单一代码生成任务的完整流程",
    request: {
      body: {
        model: "claude-3-5-sonnet-20241022",
        parallel_processing: true,
        messages: [
          {
            role: "user",
            content: "请写一个Python函数来计算斐波那契数列的第n项"
          }
        ]
      }
    }
  },
  {
    name: "复杂多任务请求",
    description: "测试多任务并行分解和处理",
    request: {
      body: {
        model: "claude-3-5-sonnet-20241022",
        parallel_processing: true,
        messages: [
          {
            role: "user",
            content: `请帮我完成以下任务：
1. 写一个JavaScript函数来实现快速排序
2. 证明快速排序的平均时间复杂度是O(n log n)
3. 将"Quick Sort Algorithm"翻译成中文和日文
4. 创作一首关于算法之美的短诗`
          }
        ]
      }
    }
  },
  {
    name: "长文本任务",
    description: "测试长文本的阶梯式模型选择",
    request: {
      body: {
        model: "claude-3-5-sonnet-20241022",
        parallel_processing: true,
        messages: [
          {
            role: "user",
            content: "请分析以下长文档的主要观点和结论：" + "This is a comprehensive analysis of artificial intelligence development trends, covering machine learning algorithms, neural network architectures, and their practical applications in various industries. ".repeat(500)
          }
        ]
      }
    }
  }
];

/**
 * 运行详细日志测试
 */
async function runDetailedLogTest() {
  console.log("🔍 开始详细日志测试...\n");
  console.log("=" * 80);
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    
    console.log(`\n📋 测试案例 ${i + 1}: ${testCase.name}`);
    console.log(`📝 描述: ${testCase.description}`);
    console.log(`📏 输入长度: ${testCase.request.body.messages[0].content.length} 字符`);
    console.log("-" * 60);
    
    try {
      console.log("\n🚀 开始执行并行任务路由器...");
      
      const parallelRouter = new ParallelTaskRouter();
      const startTime = Date.now();
      
      const result = await parallelRouter.processClaudeRequest(testCase.request, {});
      
      const totalTime = Date.now() - startTime;
      
      console.log("\n📊 执行结果摘要:");
      console.log(`   ⏱️  总执行时间: ${totalTime}ms`);
      
      if (result.success) {
        console.log(`   ✅ 执行状态: 成功`);
        console.log(`   📈 任务数量: ${result.decomposition.tasks.length}`);
        console.log(`   🔄 执行策略: ${result.decomposition.execution_strategy}`);
        console.log(`   📊 复杂度评估: ${result.decomposition.estimated_complexity}/10`);
        console.log(`   🏆 质量评分: ${result.result.quality.quality_score}/10`);
        console.log(`   ✨ 是否满意: ${result.result.quality.is_satisfactory ? '是' : '否'}`);
        
        if (result.reprocessed) {
          console.log(`   🔄 已重新处理: 是`);
        }
        
        console.log("\n📋 任务分解详情:");
        result.decomposition.tasks.forEach((task, index) => {
          console.log(`   ${index + 1}. ${task.id}: ${task.type} → ${task.recommended_model}`);
        });
        
        console.log("\n📈 任务执行结果:");
        result.taskResults.forEach((taskResult, index) => {
          console.log(`   ${index + 1}. ${taskResult.task.id}: ${taskResult.status}`);
          if (taskResult.status === 'completed') {
            console.log(`      模型: ${taskResult.result.model}`);
            console.log(`      结果预览: ${taskResult.result.content.substring(0, 50)}...`);
          } else if (taskResult.error) {
            console.log(`      错误: ${taskResult.error}`);
          }
        });
        
      } else {
        console.log(`   ❌ 执行状态: 失败`);
        console.log(`   🔧 错误信息: ${result.error}`);
        console.log(`   🛡️  兜底模型: ${result.fallbackModel}`);
      }
      
    } catch (error) {
      console.log(`\n💥 测试异常: ${error.message}`);
      console.log(`   堆栈信息: ${error.stack}`);
    }
    
    console.log("\n" + "=" * 80);
  }
  
  console.log("\n🎉 详细日志测试完成！");
}

/**
 * 测试集成路由器的详细日志
 */
async function testIntegratedRouterLogs() {
  console.log("\n🔧 测试集成路由器的详细日志...\n");
  
  // 启用并行处理
  process.env.ENABLE_PARALLEL_PROCESSING = 'true';
  
  const testRequest = {
    body: {
      model: "claude-3-5-sonnet-20241022",
      messages: [
        {
          role: "user",
          content: "请写一个Python爬虫程序来抓取网页标题，并解释其工作原理"
        }
      ]
    }
  };
  
  console.log("📤 发送请求到集成路由器...");
  console.log(`📝 请求内容: ${testRequest.body.messages[0].content}`);
  console.log(`📏 内容长度: ${testRequest.body.messages[0].content.length} 字符`);
  console.log("-" * 50);
  
  try {
    const startTime = Date.now();
    const result = await router(testRequest, {});
    const totalTime = Date.now() - startTime;
    
    console.log(`\n📊 集成路由器结果:`);
    console.log(`   ⏱️  执行时间: ${totalTime}ms`);
    
    if (result && typeof result === 'object' && result.type === 'parallel_processing') {
      console.log(`   ✅ 使用了并行处理`);
      console.log(`   📈 任务数: ${result.result.decomposition.tasks.length}`);
      console.log(`   🏆 质量评分: ${result.result.result.quality.quality_score}/10`);
      console.log(`   🔄 执行策略: ${result.result.decomposition.execution_strategy}`);
    } else {
      console.log(`   🔧 使用传统路由: ${result}`);
    }
    
  } catch (error) {
    console.log(`   ❌ 集成测试失败: ${error.message}`);
  }
  
  // 恢复环境变量
  delete process.env.ENABLE_PARALLEL_PROCESSING;
}

/**
 * 导出日志到文件
 */
async function exportDetailedLogs() {
  console.log("\n📁 导出详细日志到文件...");
  
  try {
    // 这里需要访问日志实例，暂时跳过
    console.log("   ℹ️  日志导出功能需要进一步实现");
  } catch (error) {
    console.log(`   ❌ 导出失败: ${error.message}`);
  }
}

/**
 * 主测试函数
 */
async function main() {
  console.log("🔍 详细日志测试系统");
  console.log("=" * 80);
  console.log(`📅 测试时间: ${new Date().toISOString()}`);
  console.log(`🔧 日志级别: ${process.env.LOG_LEVEL}`);
  console.log(`📋 测试用例数: ${testCases.length}`);
  console.log("=" * 80);
  
  try {
    // 运行详细日志测试
    await runDetailedLogTest();
    
    // 测试集成路由器
    await testIntegratedRouterLogs();
    
    // 导出日志
    await exportDetailedLogs();
    
    console.log("\n🎉 所有测试完成！");
    
  } catch (error) {
    console.error(`💥 测试过程中发生严重错误: ${error.message}`);
    console.error(`堆栈信息: ${error.stack}`);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  runDetailedLogTest,
  testIntegratedRouterLogs,
  exportDetailedLogs,
  main
};
