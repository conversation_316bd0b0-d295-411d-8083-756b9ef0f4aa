/**
 * 简化日志演示脚本
 * 清晰展示并行任务路由器的关键执行步骤
 */

// 设置日志级别为INFO，减少过多的DEBUG信息
process.env.LOG_LEVEL = 'INFO';

const { ParallelTaskRouter } = require('./parallel_task_router.js');

/**
 * 演示单个任务的完整流程
 */
async function demonstrateSimpleTask() {
  console.log("🎯 演示1: 简单代码生成任务");
  console.log("=" * 60);
  
  const request = {
    body: {
      model: "claude-3-5-sonnet-20241022",
      messages: [
        {
          role: "user",
          content: "请写一个Python函数来计算斐波那契数列"
        }
      ]
    }
  };
  
  console.log(`📝 用户请求: ${request.body.messages[0].content}`);
  console.log(`📏 请求长度: ${request.body.messages[0].content.length} 字符`);
  console.log("\n🚀 开始处理...\n");
  
  const router = new ParallelTaskRouter();
  const result = await router.processClaudeRequest(request, {});
  
  console.log("\n📊 处理结果:");
  if (result.success) {
    console.log(`✅ 状态: 成功`);
    console.log(`📈 任务数: ${result.decomposition.tasks.length}`);
    console.log(`🔄 策略: ${result.decomposition.execution_strategy}`);
    console.log(`🏆 质量: ${result.result.quality.quality_score}/10`);
  } else {
    console.log(`❌ 状态: 失败 - ${result.error}`);
  }
  
  console.log("\n" + "=" * 60 + "\n");
}

/**
 * 演示复杂多任务的并行处理
 */
async function demonstrateComplexTask() {
  console.log("🎯 演示2: 复杂多任务并行处理");
  console.log("=" * 60);
  
  const request = {
    body: {
      model: "claude-3-5-sonnet-20241022",
      messages: [
        {
          role: "user",
          content: `请帮我完成以下任务：
1. 写一个JavaScript快速排序函数
2. 证明快速排序的时间复杂度
3. 将"Quick Sort"翻译成中文和日文
4. 写一首关于排序算法的诗`
        }
      ]
    }
  };
  
  console.log(`📝 用户请求: 多任务复合请求`);
  console.log(`📏 请求长度: ${request.body.messages[0].content.length} 字符`);
  console.log("\n🚀 开始处理...\n");
  
  const router = new ParallelTaskRouter();
  const result = await router.processClaudeRequest(request, {});
  
  console.log("\n📊 处理结果:");
  if (result.success) {
    console.log(`✅ 状态: 成功`);
    console.log(`📈 任务数: ${result.decomposition.tasks.length}`);
    console.log(`🔄 策略: ${result.decomposition.execution_strategy}`);
    console.log(`🏆 质量: ${result.result.quality.quality_score}/10`);
    
    console.log("\n📋 任务分解详情:");
    result.decomposition.tasks.forEach((task, index) => {
      console.log(`   ${index + 1}. ${task.type} → ${task.recommended_model}`);
    });
  } else {
    console.log(`❌ 状态: 失败 - ${result.error}`);
  }
  
  console.log("\n" + "=" * 60 + "\n");
}

/**
 * 演示长文本的阶梯式处理
 */
async function demonstrateLongTextTask() {
  console.log("🎯 演示3: 长文本阶梯式处理");
  console.log("=" * 60);
  
  const longText = "请分析这份详细的技术文档：" + 
    "This comprehensive document covers advanced machine learning techniques, including deep neural networks, convolutional architectures, and their applications in computer vision and natural language processing. ".repeat(800);
  
  const request = {
    body: {
      model: "claude-3-5-sonnet-20241022",
      messages: [
        {
          role: "user",
          content: longText
        }
      ]
    }
  };
  
  console.log(`📝 用户请求: 长文本分析任务`);
  console.log(`📏 请求长度: ${request.body.messages[0].content.length} 字符`);
  console.log("\n🚀 开始处理...\n");
  
  const router = new ParallelTaskRouter();
  const result = await router.processClaudeRequest(request, {});
  
  console.log("\n📊 处理结果:");
  if (result.success) {
    console.log(`✅ 状态: 成功`);
    console.log(`📈 任务数: ${result.decomposition.tasks.length}`);
    console.log(`🔄 策略: ${result.decomposition.execution_strategy}`);
    console.log(`🏆 质量: ${result.result.quality.quality_score}/10`);
  } else {
    console.log(`❌ 状态: 失败 - ${result.error}`);
  }
  
  console.log("\n" + "=" * 60 + "\n");
}

/**
 * 主演示函数
 */
async function main() {
  console.log("🔍 并行任务路由器 - 详细日志演示");
  console.log("=" * 80);
  console.log(`📅 演示时间: ${new Date().toISOString()}`);
  console.log(`🔧 日志级别: ${process.env.LOG_LEVEL}`);
  console.log("=" * 80 + "\n");
  
  try {
    // 演示1: 简单任务
    await demonstrateSimpleTask();
    
    // 等待一下，让日志更清晰
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 演示2: 复杂任务
    await demonstrateComplexTask();
    
    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 演示3: 长文本任务
    await demonstrateLongTextTask();
    
    console.log("🎉 所有演示完成！");
    console.log("\n📋 关键观察点:");
    console.log("   1. 任务分解器如何选择合适的模型（GLM-4.5 vs Gemini-Pro）");
    console.log("   2. 任务检测模式如何识别不同类型的任务");
    console.log("   3. 并行处理器如何选择执行策略（parallel vs sequential）");
    console.log("   4. 结果汇总器如何整合多个任务结果");
    console.log("   5. 质量检查系统如何评估最终结果");
    
  } catch (error) {
    console.error(`💥 演示过程中发生错误: ${error.message}`);
    console.error(`堆栈信息: ${error.stack}`);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  demonstrateSimpleTask,
  demonstrateComplexTask,
  demonstrateLongTextTask,
  main
};
