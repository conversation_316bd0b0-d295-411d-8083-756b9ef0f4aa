/**
 * 超越单一AI的多模型协作路由策略 - 专业化模型配置版
 * 基于AiPy权威评测的智能路由系统
 * 
 * 模型能力分析（基于AiPy第三期评测报告）：
 * - GLM-4.5: 100%成功率，综合得分91分，全能冠军 (128K上下文)
 * - Qwen3-235B-A22B-Instruct-2507: 90%成功率，综合得分82分，通用能力强 (256K上下文)
 * - Qwen3-235B-A22B-Thinking-2507: 90%成功率，专门复杂推理 (256K上下文)
 * - Qwen3-Coder-480B: 90%成功率，代码生成SOTA，增强工具调用 (256K上下文)
 * - Gemini 2.5 Pro: 80%成功率，超长文本+多模态+网络搜索专家 (1M上下文)
 * - Gemini 2.5 Flash: 快速响应，时间效率111.6秒最快 (1M上下文)
 * - Kimi-K2: 80%成功率，多语言翻译专家 (200K上下文)
 * - DeepSeek-V3.1: 70%成功率，成本效率最优 (128K上下文)
 */

const fs = require('fs');
const path = require('path');

// 性能监控统计
const routingStats = {
  totalRequests: 0,
  modelUsage: new Map(),
  averageResponseTime: new Map(),
  successRate: new Map()
};

// 路由分析系统
let routerAnalytics = null;

/**
 * 初始化分析系统
 */
function initializeAnalytics() {
  if (!routerAnalytics) {
    routerAnalytics = {
      totalRequests: 0,
      routingDecisions: new Map(),
      performanceMetrics: new Map(),
      taskTypeDistribution: new Map()
    };
  }
  return routerAnalytics;
}

/**
 * 记录路由决策和性能指标
 */
function logRouting(model, description, startTime, req) {
  const duration = Date.now() - startTime;
  console.log(`[Super Router] ${description} → ${model} (Request #${routingStats.totalRequests + 1})`);
  
  // 更新统计数据
  routingStats.totalRequests++;
  const providerKey = model.split(',')[0];
  routingStats.modelUsage.set(providerKey, (routingStats.modelUsage.get(providerKey) || 0) + 1);
  routingStats.averageResponseTime.set(providerKey, duration);
  
  // 更新分析数据
  const analytics = initializeAnalytics();
  analytics.totalRequests++;
  analytics.routingDecisions.set(providerKey, (analytics.routingDecisions.get(providerKey) || 0) + 1);
  analytics.performanceMetrics.set(providerKey, duration);
}

/**
 * 任务复杂度计算函数 - 基于2025年模型评测数据优化
 */
function calculateComplexity(prompt) {
  let complexity = 0;
  
  // 基础复杂度（字符长度）
  complexity += Math.min(prompt.length / 100, 50);
  
  // 代码复杂度检测
  const codePatterns = [
    /```[\s\S]*?```/g, // 代码块
    /`[^`]+`/g, // 行内代码
    /function\s+\w+/gi, // 函数定义
    /class\s+\w+/gi, // 类定义
    /import\s+.*from/gi, // 导入语句
    /def\s+\w+/gi, // Python函数
    /public\s+class/gi, // Java类
  ];
  
  codePatterns.forEach(pattern => {
    const matches = prompt.match(pattern);
    if (matches) complexity += matches.length * 10;
  });
  
  // 数学复杂度检测
  const mathPatterns = [
    /\$\$[\s\S]*?\$\$/g, // LaTeX数学公式
    /\$[^$]+\$/g, // 行内数学公式
    /∫|∑|∏|√|∂|∇|∆/g, // 数学符号
    /证明|推导|定理|公式|计算/g, // 数学关键词
  ];
  
  mathPatterns.forEach(pattern => {
    const matches = prompt.match(pattern);
    if (matches) complexity += matches.length * 15;
  });
  
  // 多语言复杂度
  const multiLangPatterns = [
    /[一-龯]/g, // 中文字符
    /[а-яё]/gi, // 俄文字符
    /[α-ωΑ-Ω]/g, // 希腊字母
    /[ひらがなカタカナ]/g, // 日文字符
  ];
  
  let langCount = 0;
  multiLangPatterns.forEach(pattern => {
    if (pattern.test(prompt)) langCount++;
  });
  complexity += langCount * 5;
  
  return Math.round(complexity);
}

module.exports = async function router(req, config) {
  const startTime = Date.now();
  const modelName = req.body?.model || '';

  // === 调试日志记录 ===
  const logFile = path.join(__dirname, 'router_debug.log');
  const timestamp = new Date().toISOString();
  const logEntry = `${timestamp} - 路由处理: ${modelName}\n`;
  fs.appendFileSync(logFile, logEntry);

  console.log(`🔥 [路由] 处理模型: ${modelName}`);

  // === Claude模型拦截和路由 ===
  if (modelName && (modelName.includes('claude') || modelName.includes('anthropic'))) {
    console.log(`🎯 [路由] 检测到Claude模型: ${modelName}`);

    // Claude Haiku系列 → 快速响应优化
    if (modelName.includes('haiku')) {
      const model = "gemini-flash,gemini-2.5-flash";
      logRouting(model, "⚡ Claude-Haiku → Gemini Flash (快速响应)", startTime, req);
      return model;
    }

    // Opus系列 → GLM-4.5处理
    if (modelName.includes('opus')) {
      const model = "glm-4.5,ZhipuAI/GLM-4.5";
      logRouting(model, "👑 Claude-Opus → GLM-4.5 (高质量处理)", startTime, req);
      return model;
    }

    // === Claude Sonnet系列 → 完整智能路由决策系统 ===
    if (modelName.includes('sonnet')) {
      console.log(`🧠 [智能路由] Sonnet系列进入完整决策系统: ${modelName}`);
      
      const userMessages = req.body.messages?.filter((m) => m.role === "user") || [];
      if (userMessages.length === 0) {
        const model = "glm-4.5,ZhipuAI/GLM-4.5";
        logRouting(model, "🔧 无用户消息，GLM-4.5兜底", startTime, req);
        return model;
      }

      // 提取完整提示内容
      const fullPrompt = userMessages
        .map((m) => {
          if (typeof m.content === "string") return m.content;
          if (Array.isArray(m.content)) {
            return m.content.map((item) => item.text || "").join(" ");
          }
          return "";
        })
        .join(" ");

      console.log(`📝 [智能路由] 分析提示内容: ${fullPrompt.length} 字符`);
      
      // 计算任务复杂度
      const complexity = calculateComplexity(fullPrompt);
      console.log(`📊 [智能路由] 任务复杂度: ${complexity}`);

      // === 1. Web Search请求检测 ===
      if (Array.isArray(req.body.tools) &&
          req.body.tools.some((tool) => tool.type?.startsWith("web_search"))) {
        const model = "gemini-pro,gemini-2.5-pro";
        logRouting(model, "🔍 Web Search (Gemini Pro Online)", startTime, req);
        return model;
      }

      // === 2. 图像处理任务检测 ===
      const hasImages = req.body.messages?.some(m => 
        Array.isArray(m.content) && 
        m.content.some(item => item.type === 'image_url' || item.type === 'image')
      );
      
      if (hasImages || /图片|图像|照片|截图|image|photo|picture|screenshot/i.test(fullPrompt)) {
        const model = "gemini-pro,gemini-2.5-pro";
        logRouting(model, "👁️ 图像处理 (Gemini Pro Vision)", startTime, req);
        return model;
      }

      // === 3. 代码生成和编程任务检测 ===
      const codeKeywords = [
        // 中文编程关键词
        '写.*代码', '编程', '函数', '算法', '实现', '代码示例', '程序', '脚本', '开发',
        '调试', '重构', '优化代码', '代码审查', '单元测试', '集成测试',
        // 英文编程关键词
        'write.*code', 'programming', 'function', 'algorithm', 'implement', 'code example',
        'program', 'script', 'develop', 'debug', 'refactor', 'optimize code', 'code review',
        'unit test', 'integration test',
        // 技术栈关键词
        'python', 'javascript', 'java', 'c\\+\\+', 'react', 'vue', 'angular', 'node\\.js',
        'django', 'flask', 'spring', 'docker', 'kubernetes', 'git', 'github',
        // 代码结构关键词
        'class', 'interface', 'api', 'database', 'sql', 'mongodb', 'redis'
      ];
      
      const codePattern = new RegExp(codeKeywords.join('|'), 'i');
      const hasCodeBlocks = /```[\s\S]*?```/.test(fullPrompt);
      
      if (codePattern.test(fullPrompt) || hasCodeBlocks || complexity > 80) {
        const model = "qwen3-coder,Qwen/Qwen3-Coder-480B-A35B-Instruct";
        logRouting(model, "💻 代码生成 (Qwen3-Coder SOTA)", startTime, req);
        return model;
      }

      // === 4. 数学推理和复杂思考任务检测 ===
      const mathKeywords = [
        // 中文数学关键词
        '证明', '推导', '数学', '计算', '公式', '定理', '几何', '代数', '微积分',
        '统计', '概率', '逻辑', '推理', '分析', '求解',
        // 英文数学关键词
        'proof', 'derive', 'math', 'calculate', 'formula', 'theorem', 'geometry',
        'algebra', 'calculus', 'statistics', 'probability', 'logic', 'reasoning',
        'analysis', 'solve'
      ];
      
      const mathPattern = new RegExp(mathKeywords.join('|'), 'i');
      const hasMathSymbols = /[∫∑∏√∂∇∆]|\$\$[\s\S]*?\$\$|\$[^$]+\$/.test(fullPrompt);
      
      if (mathPattern.test(fullPrompt) || hasMathSymbols || complexity > 100) {
        const model = "qwen3-thinking,Qwen/Qwen3-235B-A22B-Thinking-2507";
        logRouting(model, "🧠 数学推理 (Qwen3-Thinking)", startTime, req);
        return model;
      }

      // === 5. 多语言翻译任务检测 ===
      const translationKeywords = [
        // 中文翻译关键词
        '翻译', '译成', '转换成', '用.*语', '翻译成', '译为', '转为',
        // 英文翻译关键词
        'translate', 'translation', 'convert to', 'in.*language', 'translate to',
        'translate into', 'convert into'
      ];

      const translationPattern = new RegExp(translationKeywords.join('|'), 'i');

      // 检测多语言内容
      const hasMultipleLanguages = [
        /[一-龯]/.test(fullPrompt), // 中文
        /[а-яё]/i.test(fullPrompt), // 俄文
        /[α-ωΑ-Ω]/.test(fullPrompt), // 希腊文
        /[ひらがなカタカナ]/.test(fullPrompt), // 日文
        /[가-힣]/.test(fullPrompt), // 韩文
        /[ا-ي]/.test(fullPrompt), // 阿拉伯文
      ].filter(Boolean).length > 1;

      if (translationPattern.test(fullPrompt) || hasMultipleLanguages) {
        const model = "kimi-k2,kimi-k2";
        logRouting(model, "🌍 多语言翻译 (Kimi-K2 专家)", startTime, req);
        return model;
      }

      // === 6. 创意写作任务检测 ===
      const creativeKeywords = [
        // 中文创意关键词
        '写.*故事', '创作', '小说', '诗歌', '文案', '剧本', '散文', '创意',
        '想象', '虚构', '角色', '情节', '对话',
        // 英文创意关键词
        'write.*story', 'creative', 'novel', 'poem', 'poetry', 'script', 'essay',
        'fiction', 'character', 'plot', 'dialogue', 'narrative'
      ];

      const creativePattern = new RegExp(creativeKeywords.join('|'), 'i');

      if (creativePattern.test(fullPrompt)) {
        const model = "glm-4.5,ZhipuAI/GLM-4.5";
        logRouting(model, "🎨 创意写作 (GLM-4.5 中文优化)", startTime, req);
        return model;
      }

      // === 7. 长文本和复杂分析任务检测 ===
      // Qwen3系列: 256K上下文 ≈ 200K字符
      if (fullPrompt.length > 50000 || complexity > 120) {
        const model = "qwen3-instruct,Qwen/Qwen3-235B-A22B-Instruct-2507";
        logRouting(model, "📚 长文本分析 (Qwen3-Instruct 256K)", startTime, req);
        return model;
      }

      // === 8. 超长文本任务检测 (充分利用长上下文能力) ===
      // Gemini 2.5 Pro: 1M上下文 ≈ 800K字符
      if (fullPrompt.length > 200000 || complexity > 200) {
        const model = "gemini-pro,gemini-2.5-pro";
        logRouting(model, "📖 超长文本 (Gemini Pro 1M上下文)", startTime, req);
        return model;
      }

      // === 9. 成本效率优化任务检测 ===
      const costEfficiencyKeywords = [
        // 简单任务关键词
        '简单', '基础', '入门', '概述', '简介', '什么是', '如何',
        'simple', 'basic', 'introduction', 'overview', 'what is', 'how to'
      ];

      const costEfficiencyPattern = new RegExp(costEfficiencyKeywords.join('|'), 'i');

      if (costEfficiencyPattern.test(fullPrompt) && fullPrompt.length < 2000 && complexity < 30) {
        const model = "deepseek-v3.1,deepseek-v3.1";
        logRouting(model, "💰 成本效率优化 (DeepSeek-V3.1)", startTime, req);
        return model;
      }

      // === 10. 快速响应任务检测 ===
      const quickKeywords = [
        '快速', '简要', '概括', '总结', 'quick', 'brief', 'summary'
      ];
      const quickPattern = new RegExp(quickKeywords.join('|'), 'i');

      if (quickPattern.test(fullPrompt) && fullPrompt.length < 1000) {
        const model = "gemini-flash,gemini-2.5-flash";
        logRouting(model, "⚡ 快速响应 (Gemini Flash)", startTime, req);
        return model;
      }

      // === 11. 默认兜底策略：GLM-4.5全能处理 ===
      const model = "glm-4.5,ZhipuAI/GLM-4.5";
      logRouting(model, "🏆 通用任务 (GLM-4.5 - 100% Success Rate)", startTime, req);
      return model;
    }
  }

  // === 非Claude请求的处理 ===
  console.log(`📋 [路由] 非Claude请求，返回null使用默认路由: ${modelName}`);
  return null;
};
