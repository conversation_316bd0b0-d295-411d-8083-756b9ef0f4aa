/**
 * 改进版路由器测试脚本
 * 验证阶梯式上下文选择和智能任务分解功能
 */

const router = require('./my_custom_router.js');

// 测试用例
const testCases = [
  {
    name: "短文本代码任务",
    request: {
      body: {
        model: "claude-3-5-sonnet-20241022",
        messages: [
          {
            role: "user",
            content: "请写一个Python函数来计算斐波那契数列"
          }
        ]
      }
    },
    expected: "应该选择qwen3-coder"
  },
  {
    name: "中等长度数学推理",
    request: {
      body: {
        model: "claude-3-5-sonnet-20241022", 
        messages: [
          {
            role: "user",
            content: "请证明勾股定理，并推导其数学公式。需要详细的几何分析和代数推导过程。" + "x".repeat(10000)
          }
        ]
      }
    },
    expected: "应该选择qwen3-thinking"
  },
  {
    name: "长文本翻译任务",
    request: {
      body: {
        model: "claude-3-5-sonnet-20241022",
        messages: [
          {
            role: "user", 
            content: "请将以下英文文档翻译成中文：" + "This is a very long document that needs translation. ".repeat(2000)
          }
        ]
      }
    },
    expected: "应该选择kimi-k2或更高级别模型"
  },
  {
    name: "超长文本分析",
    request: {
      body: {
        model: "claude-3-5-sonnet-20241022",
        messages: [
          {
            role: "user",
            content: "请分析这份长文档：" + "Very long content for analysis. ".repeat(15000)
          }
        ]
      }
    },
    expected: "应该选择gemini-pro"
  },
  {
    name: "Web搜索任务",
    request: {
      body: {
        model: "claude-3-5-sonnet-20241022",
        messages: [
          {
            role: "user",
            content: "请搜索最新的AI发展动态"
          }
        ],
        tools: [
          {
            type: "web_search"
          }
        ]
      }
    },
    expected: "应该选择gemini-pro"
  },
  {
    name: "简单问答任务",
    request: {
      body: {
        model: "claude-3-5-sonnet-20241022",
        messages: [
          {
            role: "user",
            content: "什么是人工智能？"
          }
        ]
      }
    },
    expected: "应该选择glm-4.5或deepseek-v3.1"
  }
];

// 运行测试
async function runTests() {
  console.log("🧪 开始测试改进版路由器...\n");
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`📋 测试 ${i + 1}: ${testCase.name}`);
    console.log(`📝 输入长度: ${testCase.request.body.messages[0].content.length} 字符`);
    console.log(`🎯 预期: ${testCase.expected}`);
    
    try {
      const result = await router(testCase.request, {});
      console.log(`✅ 实际结果: ${result}`);
      console.log("---");
    } catch (error) {
      console.log(`❌ 错误: ${error.message}`);
      console.log("---");
    }
  }
  
  console.log("🎉 测试完成！");
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests();
}

module.exports = { runTests, testCases };
