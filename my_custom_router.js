/**
 * 超越单一AI的多模型协作路由策略 - 专业化模型配置版
 * 基于AiPy权威评测的智能路由系统
 * 
 * 模型能力分析（基于AiPy第三期评测报告）：
 * - GLM-4.5: 100%成功率，综合得分91分，全能冠军 (128K上下文)
 * - Qwen3-235B-A22B-Instruct-2507: 90%成功率，综合得分82分，通用能力强 (256K上下文)
 * - Qwen3-235B-A22B-Thinking-2507: 90%成功率，专门复杂推理 (256K上下文)
 * - Qwen3-Coder-480B: 90%成功率，代码生成SOTA，增强工具调用 (256K上下文)
 * - Gemini 2.5 Pro: 80%成功率，超长文本+多模态+网络搜索专家 (1M上下文)
 * - Gemini 2.5 Flash: 快速响应，时间效率111.6秒最快 (1M上下文)
 * - Kimi-K2: 80%成功率，多语言翻译专家 (200K上下文)
 * - DeepSeek-V3.1: 70%成功率，成本效率最优 (128K上下文)
 */

const fs = require('fs');
const path = require('path');

// 性能监控统计
const routingStats = {
  totalRequests: 0,
  modelUsage: new Map(),
  averageResponseTime: new Map(),
  successRate: new Map()
};

// 路由分析系统
let routerAnalytics = null;

/**
 * 初始化分析系统
 */
function initializeAnalytics() {
  if (!routerAnalytics) {
    routerAnalytics = {
      totalRequests: 0,
      routingDecisions: new Map(),
      performanceMetrics: new Map(),
      taskTypeDistribution: new Map()
    };
  }
  return routerAnalytics;
}

/**
 * 记录路由决策和性能指标
 */
function logRouting(model, description, startTime, req) {
  const duration = Date.now() - startTime;
  console.log(`[Super Router] ${description} → ${model} (Request #${routingStats.totalRequests + 1})`);
  
  // 更新统计数据
  routingStats.totalRequests++;
  const providerKey = model.split(',')[0];
  routingStats.modelUsage.set(providerKey, (routingStats.modelUsage.get(providerKey) || 0) + 1);
  routingStats.averageResponseTime.set(providerKey, duration);
  
  // 更新分析数据
  const analytics = initializeAnalytics();
  analytics.totalRequests++;
  analytics.routingDecisions.set(providerKey, (analytics.routingDecisions.get(providerKey) || 0) + 1);
  analytics.performanceMetrics.set(providerKey, duration);
}

/**
 * 智能任务分解和复杂度评估系统
 */
function analyzeTaskComplexity(prompt) {
  const analysis = {
    totalLength: prompt.length,
    taskTypes: [],
    complexity: 0,
    recommendedModel: null,
    subtasks: []
  };

  // 任务类型检测
  const taskDetectors = [
    {
      type: 'code_generation',
      patterns: [/写.*代码|编程|函数|算法|实现|```[\s\S]*?```/gi],
      complexity: 25,
      preferredModel: 'qwen3-coder'
    },
    {
      type: 'math_reasoning',
      patterns: [/证明|推导|数学|计算|公式|定理|∫|∑|∏|√|\$\$[\s\S]*?\$\$/gi],
      complexity: 30,
      preferredModel: 'qwen3-thinking'
    },
    {
      type: 'translation',
      patterns: [/翻译|译成|translate|translation/gi],
      complexity: 15,
      preferredModel: 'kimi-k2'
    },
    {
      type: 'creative_writing',
      patterns: [/写.*故事|创作|小说|诗歌|文案|剧本/gi],
      complexity: 20,
      preferredModel: 'glm-4.5'
    },
    {
      type: 'web_search',
      patterns: [/搜索|查找|最新|实时|search|latest/gi],
      complexity: 10,
      preferredModel: 'gemini-pro'
    },
    {
      type: 'image_analysis',
      patterns: [/图片|图像|照片|截图|image|photo/gi],
      complexity: 15,
      preferredModel: 'gemini-pro'
    }
  ];

  // 检测任务类型和计算复杂度
  taskDetectors.forEach(detector => {
    detector.patterns.forEach(pattern => {
      const matches = prompt.match(pattern);
      if (matches && matches.length > 0) {
        analysis.taskTypes.push(detector.type);
        analysis.complexity += detector.complexity * matches.length;

        // 记录子任务
        matches.forEach(match => {
          analysis.subtasks.push({
            type: detector.type,
            content: match,
            preferredModel: detector.preferredModel
          });
        });
      }
    });
  });

  // 基于长度的复杂度调整
  if (analysis.totalLength > 100000) analysis.complexity += 50;
  else if (analysis.totalLength > 50000) analysis.complexity += 30;
  else if (analysis.totalLength > 10000) analysis.complexity += 15;

  return analysis;
}

/**
 * 阶梯式模型选择 - 基于上下文长度和任务复杂度
 */
function selectModelByContext(promptLength, taskAnalysis) {
  // 模型上下文限制 (字符数估算)
  const modelLimits = [
    { models: ['deepseek-v3.1', 'glm-4.5'], limit: 100000, contextSize: '128K' },
    { models: ['kimi-k2'], limit: 160000, contextSize: '200K' },
    { models: ['qwen3-instruct', 'qwen3-thinking', 'qwen3-coder'], limit: 200000, contextSize: '256K' },
    { models: ['gemini-pro', 'gemini-flash'], limit: 800000, contextSize: '1M' }
  ];

  // 根据长度选择合适的模型层级
  for (const tier of modelLimits) {
    if (promptLength <= tier.limit) {
      console.log(`📏 [阶梯选择] 长度${promptLength}字符 → ${tier.contextSize}上下文层级`);

      // 在该层级中选择最适合的模型
      if (taskAnalysis.subtasks.length > 0) {
        const preferredModel = taskAnalysis.subtasks[0].preferredModel;
        const availableModel = tier.models.find(m => m.includes(preferredModel));
        if (availableModel) {
          return getModelConfig(availableModel);
        }
      }

      // 默认选择该层级的第一个模型
      return getModelConfig(tier.models[0]);
    }
  }

  // 超长文本使用Gemini Pro
  return getModelConfig('gemini-pro');
}

/**
 * 获取模型配置
 */
function getModelConfig(modelKey) {
  const modelConfigs = {
    'glm-4.5': 'glm-4.5,ZhipuAI/GLM-4.5',
    'qwen3-instruct': 'qwen3-instruct,Qwen/Qwen3-235B-A22B-Instruct-2507',
    'qwen3-thinking': 'qwen3-thinking,Qwen/Qwen3-235B-A22B-Thinking-2507',
    'qwen3-coder': 'qwen3-coder,Qwen/Qwen3-Coder-480B-A35B-Instruct',
    'gemini-pro': 'gemini-pro,gemini-2.5-pro',
    'gemini-flash': 'gemini-flash,gemini-2.5-flash',
    'kimi-k2': 'kimi-k2,kimi-k2',
    'deepseek-v3.1': 'deepseek-v3.1,deepseek-v3.1'
  };

  return modelConfigs[modelKey] || modelConfigs['glm-4.5'];
}

/**
 * 获取模型描述信息
 */
function getModelDescription(modelConfig, promptLength, taskAnalysis) {
  const modelKey = modelConfig.split(',')[0];
  const contextInfo = promptLength > 100000 ? `${Math.round(promptLength/1000)}K字符` : `${promptLength}字符`;
  const taskInfo = taskAnalysis.taskTypes.length > 0 ? `[${taskAnalysis.taskTypes.join(',')}]` : '通用任务';

  const descriptions = {
    'glm-4.5': `🏆 GLM-4.5全能处理 (${contextInfo}, ${taskInfo})`,
    'qwen3-instruct': `📚 Qwen3-Instruct长文本 (${contextInfo}, ${taskInfo})`,
    'qwen3-thinking': `🧠 Qwen3-Thinking推理 (${contextInfo}, ${taskInfo})`,
    'qwen3-coder': `💻 Qwen3-Coder编程 (${contextInfo}, ${taskInfo})`,
    'gemini-pro': `🌟 Gemini-Pro超长文本 (${contextInfo}, ${taskInfo})`,
    'gemini-flash': `⚡ Gemini-Flash快速响应 (${contextInfo}, ${taskInfo})`,
    'kimi-k2': `🌍 Kimi-K2多语言 (${contextInfo}, ${taskInfo})`,
    'deepseek-v3.1': `💰 DeepSeek-V3.1成本优化 (${contextInfo}, ${taskInfo})`
  };

  return descriptions[modelKey] || `🔧 ${modelKey} (${contextInfo}, ${taskInfo})`;
}

module.exports = async function router(req, config) {
  const startTime = Date.now();
  const modelName = req.body?.model || '';

  // === 调试日志记录 ===
  const logFile = path.join(__dirname, 'router_debug.log');
  const timestamp = new Date().toISOString();
  const logEntry = `${timestamp} - 路由处理: ${modelName}\n`;
  fs.appendFileSync(logFile, logEntry);

  console.log(`🔥 [路由] 处理模型: ${modelName}`);

  // === Claude模型拦截和路由 ===
  if (modelName && (modelName.includes('claude') || modelName.includes('anthropic'))) {
    console.log(`🎯 [路由] 检测到Claude模型: ${modelName}`);

    // Claude Haiku系列 → 快速响应优化
    if (modelName.includes('haiku')) {
      const model = "gemini-flash,gemini-2.5-flash";
      logRouting(model, "⚡ Claude-Haiku → Gemini Flash (快速响应)", startTime, req);
      return model;
    }

    // Opus系列 → GLM-4.5处理
    if (modelName.includes('opus')) {
      const model = "glm-4.5,ZhipuAI/GLM-4.5";
      logRouting(model, "👑 Claude-Opus → GLM-4.5 (高质量处理)", startTime, req);
      return model;
    }

    // === Claude Sonnet系列 → 完整智能路由决策系统 ===
    if (modelName.includes('sonnet')) {
      console.log(`🧠 [智能路由] Sonnet系列进入完整决策系统: ${modelName}`);
      
      const userMessages = req.body.messages?.filter((m) => m.role === "user") || [];
      if (userMessages.length === 0) {
        const model = "glm-4.5,ZhipuAI/GLM-4.5";
        logRouting(model, "🔧 无用户消息，GLM-4.5兜底", startTime, req);
        return model;
      }

      // 提取完整提示内容
      const fullPrompt = userMessages
        .map((m) => {
          if (typeof m.content === "string") return m.content;
          if (Array.isArray(m.content)) {
            return m.content.map((item) => item.text || "").join(" ");
          }
          return "";
        })
        .join(" ");

      console.log(`📝 [智能路由] 分析提示内容: ${fullPrompt.length} 字符`);

      // 智能任务分析
      const taskAnalysis = analyzeTaskComplexity(fullPrompt);
      console.log(`📊 [智能路由] 任务分析:`, {
        类型: taskAnalysis.taskTypes,
        复杂度: taskAnalysis.complexity,
        子任务数: taskAnalysis.subtasks.length
      });

      // === 1. 优先级任务检测 (必须使用特定模型) ===

      // Web Search请求检测
      if (Array.isArray(req.body.tools) &&
          req.body.tools.some((tool) => tool.type?.startsWith("web_search"))) {
        const model = "gemini-pro,gemini-2.5-pro";
        logRouting(model, "🔍 Web Search (Gemini Pro Online)", startTime, req);
        return model;
      }

      // 图像处理任务检测
      const hasImages = req.body.messages?.some(m =>
        Array.isArray(m.content) &&
        m.content.some(item => item.type === 'image_url' || item.type === 'image')
      );

      if (hasImages || taskAnalysis.taskTypes.includes('image_analysis')) {
        const model = "gemini-pro,gemini-2.5-pro";
        logRouting(model, "👁️ 图像处理 (Gemini Pro Vision)", startTime, req);
        return model;
      }

      // === 2. 阶梯式智能路由系统 ===

      // 特殊任务类型优先处理
      if (taskAnalysis.taskTypes.includes('code_generation')) {
        // 检查是否在合适的上下文范围内
        if (fullPrompt.length <= 200000) {
          const model = "qwen3-coder,Qwen/Qwen3-Coder-480B-A35B-Instruct";
          logRouting(model, "💻 代码生成 (Qwen3-Coder SOTA)", startTime, req);
          return model;
        }
      }

      if (taskAnalysis.taskTypes.includes('math_reasoning')) {
        if (fullPrompt.length <= 200000) {
          const model = "qwen3-thinking,Qwen/Qwen3-235B-A22B-Thinking-2507";
          logRouting(model, "🧠 数学推理 (Qwen3-Thinking)", startTime, req);
          return model;
        }
      }

      if (taskAnalysis.taskTypes.includes('translation')) {
        if (fullPrompt.length <= 160000) {
          const model = "kimi-k2,kimi-k2";
          logRouting(model, "🌍 多语言翻译 (Kimi-K2 专家)", startTime, req);
          return model;
        }
      }

      if (taskAnalysis.taskTypes.includes('creative_writing')) {
        if (fullPrompt.length <= 100000) {
          const model = "glm-4.5,ZhipuAI/GLM-4.5";
          logRouting(model, "🎨 创意写作 (GLM-4.5 中文优化)", startTime, req);
          return model;
        }
      }

      // === 3. 阶梯式上下文长度路由 ===
      const selectedModel = selectModelByContext(fullPrompt.length, taskAnalysis);
      const modelDescription = getModelDescription(selectedModel, fullPrompt.length, taskAnalysis);

      logRouting(selectedModel, modelDescription, startTime, req);
      return selectedModel;
    }
  }

  // === 非Claude请求的处理 ===
  console.log(`📋 [路由] 非Claude请求，返回null使用默认路由: ${modelName}`);
  return null;
};
