/**
 * Claude Code Router - 官方模板风格的简洁路由器
 * 基于CCR官方模板，专注核心任务分配，不过度干预Claude Code集成功能
 *
 * @param {object} req - The request object from Claude Code, containing the request body.
 * @param {object} config - The application's config object.
 * @returns {Promise<string|null>} - A promise that resolves to the "provider,model_name" string, or null to use the default router.
 */
module.exports = async function router(req, config) {
  // 获取用户消息内容
  const userMessage = req.body.messages?.find(
    (m) => m.role === "user"
  )?.content;
  const modelName = req.body?.model || "";

  // 如果没有用户消息，使用默认路由
  if (!userMessage) {
    return null;
  }

  // 将消息内容转换为字符串（处理数组格式的content）
  let messageText = "";
  if (typeof userMessage === "string") {
    messageText = userMessage;
  } else if (Array.isArray(userMessage)) {
    messageText = userMessage.map((item) => item.text || "").join(" ");
  }

  console.log(`🔍 [CCR Router] 处理请求: ${messageText.length} 字符`);

  // === Claude Haiku → Gemini Flash (快速响应) ===
  if (modelName.includes("haiku")) {
    console.log(`⚡ [CCR Router] Haiku → Gemini Flash`);
    return "gemini-flash,gemini-2.5-flash";
  }

  // === 只对Claude模型进行路由，其他模型使用默认 ===
  if (!modelName.includes("claude")) {
    return null;
  }

  // === 1. 前端开发任务 → Qwen3-Coder ===
  const frontendKeywords = [
    // 基础前端技术
    "html", "css", "javascript", "js", "typescript", "ts",
    // 前端框架
    "react", "vue", "angular", "svelte", "next.js", "nuxt", "gatsby",
    // 前端工具
    "webpack", "vite", "rollup", "babel", "eslint", "prettier",
    // 前端库
    "jquery", "bootstrap", "tailwind", "sass", "less", "styled-components",
    // 前端概念
    "dom", "响应式", "组件", "路由", "状态管理", "前端", "网页", "浏览器",
    // 中文关键词
    "前端开发", "网页开发", "界面开发", "用户界面", "UI开发"
  ];

  // === 2. 后端开发任务 → Qwen3-Coder ===
  const backendKeywords = [
    // 后端语言
    "python", "java", "c#", "php", "ruby", "go", "rust", "kotlin", "scala",
    // 后端框架
    "django", "flask", "fastapi", "spring", "express", "koa", "gin", "fiber",
    // 数据库
    "mysql", "postgresql", "mongodb", "redis", "sqlite", "oracle", "数据库",
    // 后端概念
    "api", "rest", "graphql", "微服务", "docker", "kubernetes", "nginx",
    "服务器", "后端", "接口", "数据库设计", "架构设计",
    // 中文关键词
    "后端开发", "服务端", "接口开发", "数据库开发", "系统架构"
  ];

  // === 3. 桌面应用开发任务 → Qwen3-Coder ===
  const desktopKeywords = [
    // 桌面开发技术
    "electron", "tauri", "flutter", "qt", "gtk", "wpf", "winforms",
    // 桌面开发语言
    "c++", "c#", "java swing", "javafx", "python tkinter", "pyqt",
    // 桌面应用概念
    "桌面应用", "客户端", "gui", "窗口", "桌面软件", "本地应用",
    // 中文关键词
    "桌面开发", "客户端开发", "桌面程序", "窗口程序", "本地软件"
  ];

  // === 4. 通用代码任务 → Qwen3-Coder ===
  const generalCodeKeywords = [
    // 通用编程概念
    "explain this code", "代码解释", "代码审查", "代码优化", "重构",
    "写代码", "编程", "函数", "方法", "类", "对象", "算法", "数据结构",
    "调试", "测试", "单元测试", "集成测试", "性能优化", "代码规范",
    // 编程语言通用
    "变量", "循环", "条件", "异常", "模块", "包", "库", "框架",
    "开发", "实现", "功能", "需求", "bug", "修复", "部署"
  ];

  // 检查是否包含代码块
  const hasCodeBlock = /```[\s\S]*?```/.test(messageText);

  // 检查各类开发任务
  const isFrontend = frontendKeywords.some(keyword =>
    messageText.toLowerCase().includes(keyword.toLowerCase())
  );
  const isBackend = backendKeywords.some(keyword =>
    messageText.toLowerCase().includes(keyword.toLowerCase())
  );
  const isDesktop = desktopKeywords.some(keyword =>
    messageText.toLowerCase().includes(keyword.toLowerCase())
  );
  const isGeneralCode = generalCodeKeywords.some(keyword =>
    messageText.toLowerCase().includes(keyword.toLowerCase())
  ) || hasCodeBlock;

  // 如果匹配任何代码相关任务，使用Qwen3-Coder
  if (isFrontend || isBackend || isDesktop || isGeneralCode) {
    let taskType = "通用代码";
    if (isFrontend) taskType = "前端开发";
    else if (isBackend) taskType = "后端开发";
    else if (isDesktop) taskType = "桌面开发";

    console.log(`💻 [CCR Router] ${taskType}任务 → Qwen3-Coder`);
    return "qwen3-coder,Qwen/Qwen3-Coder-480B-A35B-Instruct";
  }

  // === 2. 翻译任务 → Kimi-K2 ===
  if (
    messageText.includes("翻译") ||
    messageText.includes("translate") ||
    messageText.includes("译成") ||
    messageText.includes("转换成")
  ) {
    console.log(`🌍 [CCR Router] 翻译任务 → Kimi-K2`);
    return "kimi-k2,kimi-k2";
  }

  // === 3. 图像处理任务 → Gemini Pro ===
  const hasImages = req.body.messages?.some(
    (m) =>
      Array.isArray(m.content) &&
      m.content.some(
        (item) =>
          item.type === "image_url" ||
          item.type === "image" ||
          item.type === "jpg" ||
          item.type === "png"
      )
  );

  if (
    hasImages ||
    messageText.includes("图片") ||
    messageText.includes("image")
  ) {
    console.log(`👁️ [CCR Router] 图像处理 → Gemini Flash`);
    return "gemini-pro,gemini-2.5-flash";
  }

  return null;
};
