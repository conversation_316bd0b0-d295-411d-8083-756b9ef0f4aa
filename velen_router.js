/**
 * Claude Code Router - 官方模板风格的简洁路由器
 * 基于CCR官方模板，专注核心任务分配，不过度干预Claude Code集成功能
 *
 * @param {object} req - The request object from Claude Code, containing the request body.
 * @param {object} config - The application's config object.
 * @returns {Promise<string|null>} - A promise that resolves to the "provider,model_name" string, or null to use the default router.
 */
module.exports = async function router(req, config) {
  // 获取用户消息内容
  const userMessage = req.body.messages?.find(
    (m) => m.role === "user"
  )?.content;
  const modelName = req.body?.model || "";

  // 如果没有用户消息，使用默认路由
  if (!userMessage) {
    return null;
  }

  // 将消息内容转换为字符串（处理数组格式的content）
  let messageText = "";
  if (typeof userMessage === "string") {
    messageText = userMessage;
  } else if (Array.isArray(userMessage)) {
    messageText = userMessage.map((item) => item.text || "").join(" ");
  }

  console.log(`🔍 [CCR Router] 处理请求: ${messageText.length} 字符`);

  // === Claude Haiku → Gemini Flash (快速响应) ===
  if (modelName.includes("haiku")) {
    console.log(`⚡ [CCR Router] Haiku → Gemini Flash`);
    return "gemini-flash,gemini-2.5-flash";
  }

  // === 只对Claude模型进行路由，其他模型使用默认 ===
  if (!modelName.includes("claude")) {
    return null;
  }

  // === 1. 代码相关任务 → Qwen3-Coder ===
  if (
    messageText.includes("explain this code") ||
    messageText.includes("写代码") ||
    messageText.includes("编程") ||
    messageText.includes("函数") ||
    messageText.includes("算法") ||
    /```[\s\S]*?```/.test(messageText)
  ) {
    console.log(`💻 [CCR Router] 代码任务 → Qwen3-Coder`);
    return "qwen3-coder,Qwen/Qwen3-Coder-480B-A35B-Instruct";
  }

  // === 2. 翻译任务 → Kimi-K2 ===
  if (
    messageText.includes("翻译") ||
    messageText.includes("translate") ||
    messageText.includes("译成") ||
    messageText.includes("转换成")
  ) {
    console.log(`🌍 [CCR Router] 翻译任务 → Kimi-K2`);
    return "kimi-k2,kimi-k2";
  }

  // === 3. 图像处理任务 → Gemini Pro ===
  const hasImages = req.body.messages?.some(
    (m) =>
      Array.isArray(m.content) &&
      m.content.some(
        (item) =>
          item.type === "image_url" ||
          item.type === "image" ||
          item.type === "jpg" ||
          item.type === "png"
      )
  );

  if (
    hasImages ||
    messageText.includes("图片") ||
    messageText.includes("image")
  ) {
    console.log(`👁️ [CCR Router] 图像处理 → Gemini Flash`);
    return "gemini-pro,gemini-2.5-flash";
  }

  return null;
};
