/**
 * Claude Code Router - 官方模板风格的简洁路由器
 * 基于CCR官方模板，专注核心任务分配，不过度干预Claude Code集成功能
 *
 * @param {object} req - The request object from Claude Code, containing the request body.
 * @param {object} config - The application's config object.
 * @returns {Promise<string|null>} - A promise that resolves to the "provider,model_name" string, or null to use the default router.
 */
module.exports = async function router(req, config) {
  // 获取用户消息内容
  const userMessage = req.body.messages?.find((m) => m.role === "user")?.content;
  const modelName = req.body?.model || '';

  // 如果没有用户消息，使用默认路由
  if (!userMessage) {
    return null;
  }

  // 将消息内容转换为字符串（处理数组格式的content）
  let messageText = '';
  if (typeof userMessage === 'string') {
    messageText = userMessage;
  } else if (Array.isArray(userMessage)) {
    messageText = userMessage.map(item => item.text || '').join(' ');
  }

  console.log(`🔍 [CCR Router] 处理请求: ${messageText.length} 字符`);

  // === 1. Claude Haiku → Gemini Flash (快速响应) ===
  if (modelName.includes('haiku')) {
    console.log(`⚡ [CCR Router] Haiku → Gemini Flash`);
    return "gemini-flash,gemini-2.5-flash";
  }

  // === 2. 只对Claude模型进行路由，其他模型使用默认 ===
  if (!modelName.includes('claude') && !modelName.includes('anthropic')) {
    return null;
  }

  // === 3. 代码相关任务 → Qwen3-Coder ===
  if (messageText.includes("explain this code") || 
      messageText.includes("写代码") || 
      messageText.includes("编程") ||
      messageText.includes("函数") ||
      messageText.includes("算法") ||
      /```[\s\S]*?```/.test(messageText)) {
    console.log(`💻 [CCR Router] 代码任务 → Qwen3-Coder`);
    return "qwen3-coder,Qwen/Qwen3-Coder-480B-A35B-Instruct";
  }

  // === 4. 数学推理任务 → Qwen3-Thinking ===
  if (messageText.includes("证明") || 
      messageText.includes("推导") ||
      messageText.includes("数学") ||
      messageText.includes("计算") ||
      messageText.includes("公式") ||
      /[∫∑∏√∂∇∆]|\$\$[\s\S]*?\$\$/.test(messageText)) {
    console.log(`🧠 [CCR Router] 数学推理 → Qwen3-Thinking`);
    return "qwen3-thinking,Qwen/Qwen3-235B-A22B-Thinking-2507";
  }

  // === 5. 翻译任务 → Kimi-K2 ===
  if (messageText.includes("翻译") || 
      messageText.includes("translate") ||
      messageText.includes("译成") ||
      messageText.includes("转换成")) {
    console.log(`🌍 [CCR Router] 翻译任务 → Kimi-K2`);
    return "kimi-k2,kimi-k2";
  }

  // === 6. Web搜索任务 → Gemini Pro ===
  if (Array.isArray(req.body.tools) &&
      req.body.tools.some((tool) => tool.type?.startsWith("web_search"))) {
    console.log(`🔍 [CCR Router] Web搜索 → Gemini Pro`);
    return "gemini-pro,gemini-2.5-pro";
  }

  // === 7. 图像处理任务 → Gemini Pro ===
  const hasImages = req.body.messages?.some(m => 
    Array.isArray(m.content) && 
    m.content.some(item => item.type === 'image_url' || item.type === 'image')
  );
  
  if (hasImages || messageText.includes("图片") || messageText.includes("image")) {
    console.log(`👁️ [CCR Router] 图像处理 → Gemini Pro`);
    return "gemini-pro,gemini-2.5-pro";
  }

  // === 8. 超长文本 → Gemini Pro ===
  if (messageText.length > 50000) {
    console.log(`📖 [CCR Router] 超长文本 → Gemini Pro`);
    return "gemini-pro,gemini-2.5-pro";
  }

  // === 9. 长文本 → Qwen3-Instruct ===
  if (messageText.length > 10000) {
    console.log(`📚 [CCR Router] 长文本 → Qwen3-Instruct`);
    return "qwen3-instruct,Qwen/Qwen3-235B-A22B-Instruct-2507";
  }

  // === 10. 简单任务 → DeepSeek-V3.1 (成本优化) ===
  const simpleKeywords = ['什么是', '简单', '基础', '入门', 'what is', 'simple', 'basic'];
  if (messageText.length < 1000 && 
      simpleKeywords.some(keyword => messageText.includes(keyword))) {
    console.log(`💰 [CCR Router] 简单任务 → DeepSeek-V3.1`);
    return "deepseek-v3.1,deepseek-v3.1";
  }

  // === 11. 默认使用GLM-4.5 (全能冠军) ===
  console.log(`🏆 [CCR Router] 通用任务 → GLM-4.5`);
  return "glm-4.5,ZhipuAI/GLM-4.5";
};
