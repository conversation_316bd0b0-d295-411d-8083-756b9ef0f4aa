/**
 * 并行任务分解和处理系统
 * 实现真正的多模型协作和并行处理机制
 */

// const axios = require('axios'); // 暂时注释掉，使用模拟API调用

/**
 * 详细日志记录器
 */
class DetailedLogger {
  constructor() {
    this.logLevel = process.env.LOG_LEVEL || 'INFO'; // DEBUG, INFO, WARN, ERROR
    this.enableTimestamp = true;
    this.enableColors = true;
    this.logHistory = [];
  }

  /**
   * 格式化时间戳
   */
  getTimestamp() {
    if (!this.enableTimestamp) return '';
    const now = new Date();
    return `[${now.toISOString()}]`;
  }

  /**
   * 获取颜色代码
   */
  getColor(level) {
    if (!this.enableColors) return '';
    const colors = {
      DEBUG: '\x1b[36m', // 青色
      INFO: '\x1b[32m',  // 绿色
      WARN: '\x1b[33m',  // 黄色
      ERROR: '\x1b[31m', // 红色
      RESET: '\x1b[0m'   // 重置
    };
    return colors[level] || colors.RESET;
  }

  /**
   * 记录日志
   */
  log(level, component, step, message, data = null) {
    const timestamp = this.getTimestamp();
    const color = this.getColor(level);
    const reset = this.getColor('RESET');

    let logMessage = `${color}${timestamp} [${level}] [${component}] ${step}: ${message}${reset}`;

    if (data) {
      logMessage += `\n${color}   数据: ${JSON.stringify(data, null, 2)}${reset}`;
    }

    console.log(logMessage);

    // 保存到历史记录
    this.logHistory.push({
      timestamp: new Date().toISOString(),
      level,
      component,
      step,
      message,
      data
    });
  }

  debug(component, step, message, data = null) {
    if (this.logLevel === 'DEBUG') {
      this.log('DEBUG', component, step, message, data);
    }
  }

  info(component, step, message, data = null) {
    if (['DEBUG', 'INFO'].includes(this.logLevel)) {
      this.log('INFO', component, step, message, data);
    }
  }

  warn(component, step, message, data = null) {
    if (['DEBUG', 'INFO', 'WARN'].includes(this.logLevel)) {
      this.log('WARN', component, step, message, data);
    }
  }

  error(component, step, message, data = null) {
    this.log('ERROR', component, step, message, data);
  }

  /**
   * 获取日志历史
   */
  getHistory() {
    return this.logHistory;
  }

  /**
   * 清空日志历史
   */
  clearHistory() {
    this.logHistory = [];
  }

  /**
   * 导出日志到文件
   */
  exportLogs(filename = 'parallel_router_logs.json') {
    const fs = require('fs');
    try {
      fs.writeFileSync(filename, JSON.stringify(this.logHistory, null, 2));
      this.info('Logger', 'exportLogs', `日志已导出到 ${filename}`);
    } catch (error) {
      this.error('Logger', 'exportLogs', `导出日志失败: ${error.message}`);
    }
  }
}

// 全局日志实例
const logger = new DetailedLogger();

/**
 * 任务分解器 - 使用GLM-4.5或Gemini Pro进行智能任务分解
 */
class TaskDecomposer {
  constructor() {
    this.decomposerPrompt = `你是一个专业的任务分解专家。请将用户的复杂请求分解成多个独立的子任务。

分解规则：
1. 每个子任务应该是独立的，可以并行处理
2. 识别任务类型：code_generation, math_reasoning, translation, creative_writing, image_analysis, web_search, data_analysis
3. 为每个子任务推荐最适合的模型

输出格式（严格JSON）：
{
  "tasks": [
    {
      "id": "task_1",
      "type": "code_generation",
      "content": "具体任务内容",
      "recommended_model": "qwen3-coder",
      "priority": 1,
      "dependencies": []
    }
  ],
  "execution_strategy": "parallel" | "sequential",
  "estimated_complexity": 1-10
}

用户请求：`;
  }

  /**
   * 选择分解器模型
   */
  selectDecomposerModel(promptLength) {
    logger.debug('TaskDecomposer', 'selectDecomposerModel', '开始选择分解器模型', { promptLength });

    // GLM-4.5上下文限制：128K ≈ 100K字符
    let selectedModel;
    if (promptLength <= 100000) {
      selectedModel = {
        model: "glm-4.5,ZhipuAI/GLM-4.5",
        name: "GLM-4.5",
        contextLimit: 100000,
        reason: "输入长度在GLM-4.5上下文限制内"
      };
      logger.info('TaskDecomposer', 'selectDecomposerModel', '选择GLM-4.5作为分解器', selectedModel);
    } else {
      selectedModel = {
        model: "gemini-pro,gemini-2.5-pro",
        name: "Gemini-Pro",
        contextLimit: 800000,
        reason: "输入长度超过GLM-4.5限制，使用Gemini Pro"
      };
      logger.info('TaskDecomposer', 'selectDecomposerModel', '选择Gemini Pro作为分解器', selectedModel);
    }

    return selectedModel;
  }

  /**
   * 分解任务
   */
  async decomposeTasks(userPrompt, apiConfig) {
    logger.info('TaskDecomposer', 'decomposeTasks', '开始任务分解流程', {
      promptLength: userPrompt.length,
      promptPreview: userPrompt.substring(0, 100) + (userPrompt.length > 100 ? '...' : '')
    });

    const decomposer = this.selectDecomposerModel(userPrompt.length);
    logger.info('TaskDecomposer', 'decomposeTasks', `选定分解器: ${decomposer.name}`, decomposer);

    const fullPrompt = this.decomposerPrompt + userPrompt;
    logger.debug('TaskDecomposer', 'decomposeTasks', '构建完整提示', {
      promptLength: this.decomposerPrompt.length,
      userPromptLength: userPrompt.length,
      fullPromptLength: fullPrompt.length
    });

    try {
      logger.info('TaskDecomposer', 'decomposeTasks', '开始调用分解器模型（模拟）');

      // 这里需要实际调用模型API
      // 暂时返回模拟的分解结果，实际使用时需要替换为真实API调用
      const mockDecomposition = this.generateMockDecomposition(userPrompt);

      logger.info('TaskDecomposer', 'decomposeTasks', '任务分解完成', {
        taskCount: mockDecomposition.tasks.length,
        executionStrategy: mockDecomposition.execution_strategy,
        complexity: mockDecomposition.estimated_complexity
      });

      // 详细记录每个子任务
      mockDecomposition.tasks.forEach((task, index) => {
        logger.debug('TaskDecomposer', 'decomposeTasks', `子任务${index + 1}详情`, task);
      });

      return mockDecomposition;
    } catch (error) {
      logger.error('TaskDecomposer', 'decomposeTasks', '任务分解失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 生成模拟分解结果（实际使用时应替换为真实API调用）
   */
  generateMockDecomposition(userPrompt) {
    logger.info('TaskDecomposer', 'generateMockDecomposition', '开始模拟任务分解', {
      promptLength: userPrompt.length
    });

    const tasks = [];
    let taskId = 1;
    const detectionResults = [];

    // 检测代码任务
    const codePattern = /写.*代码|编程|函数|算法|```/i;
    if (codePattern.test(userPrompt)) {
      const task = {
        id: `task_${taskId++}`,
        type: "code_generation",
        content: "生成代码实现",
        recommended_model: "qwen3-coder",
        priority: 1,
        dependencies: []
      };
      tasks.push(task);
      detectionResults.push({ pattern: '代码生成', matched: true, task: task.id });
      logger.debug('TaskDecomposer', 'generateMockDecomposition', '检测到代码生成任务', task);
    } else {
      detectionResults.push({ pattern: '代码生成', matched: false });
    }

    // 检测数学任务
    const mathPattern = /证明|推导|数学|计算|公式/i;
    if (mathPattern.test(userPrompt)) {
      const task = {
        id: `task_${taskId++}`,
        type: "math_reasoning",
        content: "数学推理和计算",
        recommended_model: "qwen3-thinking",
        priority: 1,
        dependencies: []
      };
      tasks.push(task);
      detectionResults.push({ pattern: '数学推理', matched: true, task: task.id });
      logger.debug('TaskDecomposer', 'generateMockDecomposition', '检测到数学推理任务', task);
    } else {
      detectionResults.push({ pattern: '数学推理', matched: false });
    }

    // 检测翻译任务
    const translationPattern = /翻译|translate/i;
    if (translationPattern.test(userPrompt)) {
      const task = {
        id: `task_${taskId++}`,
        type: "translation",
        content: "语言翻译",
        recommended_model: "kimi-k2",
        priority: 1,
        dependencies: []
      };
      tasks.push(task);
      detectionResults.push({ pattern: '翻译', matched: true, task: task.id });
      logger.debug('TaskDecomposer', 'generateMockDecomposition', '检测到翻译任务', task);
    } else {
      detectionResults.push({ pattern: '翻译', matched: false });
    }

    // 检测创意写作
    const creativePattern = /写.*故事|创作|小说|文案/i;
    if (creativePattern.test(userPrompt)) {
      const task = {
        id: `task_${taskId++}`,
        type: "creative_writing",
        content: "创意写作",
        recommended_model: "glm-4.5",
        priority: 1,
        dependencies: []
      };
      tasks.push(task);
      detectionResults.push({ pattern: '创意写作', matched: true, task: task.id });
      logger.debug('TaskDecomposer', 'generateMockDecomposition', '检测到创意写作任务', task);
    } else {
      detectionResults.push({ pattern: '创意写作', matched: false });
    }

    // 如果没有检测到特定任务，创建通用任务
    if (tasks.length === 0) {
      const task = {
        id: `task_${taskId++}`,
        type: "general",
        content: userPrompt,
        recommended_model: "glm-4.5",
        priority: 1,
        dependencies: []
      };
      tasks.push(task);
      logger.info('TaskDecomposer', 'generateMockDecomposition', '未检测到特定任务类型，创建通用任务', task);
    }

    const executionStrategy = tasks.length > 1 ? "parallel" : "sequential";
    const estimatedComplexity = Math.min(tasks.length * 2, 10);

    const decomposition = {
      tasks,
      execution_strategy: executionStrategy,
      estimated_complexity: estimatedComplexity
    };

    logger.info('TaskDecomposer', 'generateMockDecomposition', '任务分解完成', {
      totalTasks: tasks.length,
      executionStrategy,
      estimatedComplexity,
      detectionResults
    });

    return decomposition;
  }
}

/**
 * 并行处理引擎
 */
class ParallelProcessor {
  constructor() {
    this.modelConfigs = {
      'qwen3-coder': 'qwen3-coder,Qwen/Qwen3-Coder-480B-A35B-Instruct',
      'qwen3-thinking': 'qwen3-thinking,Qwen/Qwen3-235B-A22B-Thinking-2507',
      'qwen3-instruct': 'qwen3-instruct,Qwen/Qwen3-235B-A22B-Instruct-2507',
      'glm-4.5': 'glm-4.5,ZhipuAI/GLM-4.5',
      'gemini-pro': 'gemini-pro,gemini-2.5-pro',
      'gemini-flash': 'gemini-flash,gemini-2.5-flash',
      'kimi-k2': 'kimi-k2,kimi-k2',
      'deepseek-v3.1': 'deepseek-v3.1,deepseek-v3.1'
    };
  }

  /**
   * 并行执行任务
   */
  async executeTasks(decomposition, originalRequest, apiConfig) {
    const { tasks, execution_strategy } = decomposition;

    logger.info('ParallelProcessor', 'executeTasks', '开始任务执行流程', {
      taskCount: tasks.length,
      executionStrategy: execution_strategy,
      estimatedComplexity: decomposition.estimated_complexity
    });

    // 记录每个任务的详细信息
    tasks.forEach((task, index) => {
      logger.debug('ParallelProcessor', 'executeTasks', `任务${index + 1}准备执行`, {
        taskId: task.id,
        type: task.type,
        recommendedModel: task.recommended_model,
        priority: task.priority,
        dependencies: task.dependencies
      });
    });

    if (execution_strategy === "parallel") {
      logger.info('ParallelProcessor', 'executeTasks', '采用并行执行策略');
      return await this.executeParallel(tasks, originalRequest, apiConfig);
    } else {
      logger.info('ParallelProcessor', 'executeTasks', '采用顺序执行策略');
      return await this.executeSequential(tasks, originalRequest, apiConfig);
    }
  }

  /**
   * 并行执行
   */
  async executeParallel(tasks, originalRequest, apiConfig) {
    const results = new Map();
    const promises = tasks.map(task => this.executeTask(task, originalRequest, apiConfig));
    
    try {
      const taskResults = await Promise.all(promises);
      tasks.forEach((task, index) => {
        results.set(task.id, {
          task,
          result: taskResults[index],
          status: 'completed'
        });
      });
      
      console.log(`✅ [并行处理] 所有${tasks.length}个任务并行完成`);
      return results;
    } catch (error) {
      console.error(`❌ [并行处理] 并行执行失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 顺序执行
   */
  async executeSequential(tasks, originalRequest, apiConfig) {
    const results = new Map();
    
    for (const task of tasks) {
      try {
        const result = await this.executeTask(task, originalRequest, apiConfig);
        results.set(task.id, {
          task,
          result,
          status: 'completed'
        });
        console.log(`✅ [顺序处理] 任务${task.id}完成`);
      } catch (error) {
        console.error(`❌ [顺序处理] 任务${task.id}失败: ${error.message}`);
        results.set(task.id, {
          task,
          result: null,
          status: 'failed',
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * 执行单个任务
   */
  async executeTask(task, originalRequest, apiConfig) {
    const startTime = Date.now();
    const modelConfig = this.modelConfigs[task.recommended_model] || this.modelConfigs['glm-4.5'];

    logger.info('ParallelProcessor', 'executeTask', `开始执行任务 ${task.id}`, {
      taskId: task.id,
      taskType: task.type,
      recommendedModel: task.recommended_model,
      actualModelConfig: modelConfig,
      taskContent: task.content
    });

    try {
      // 这里需要实际调用模型API
      // 暂时返回模拟结果，实际使用时需要替换为真实API调用
      const result = await this.simulateTaskExecution(task, modelConfig);

      const executionTime = Date.now() - startTime;
      logger.info('ParallelProcessor', 'executeTask', `任务 ${task.id} 执行成功`, {
        taskId: task.id,
        executionTime: `${executionTime}ms`,
        resultPreview: result.content.substring(0, 100) + (result.content.length > 100 ? '...' : '')
      });

      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error('ParallelProcessor', 'executeTask', `任务 ${task.id} 执行失败`, {
        taskId: task.id,
        executionTime: `${executionTime}ms`,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 模拟任务执行（实际使用时应替换为真实API调用）
   */
  async simulateTaskExecution(task, modelConfig) {
    logger.debug('ParallelProcessor', 'simulateTaskExecution', `模拟执行任务 ${task.id}`, {
      taskId: task.id,
      modelConfig
    });

    // 模拟API调用延迟
    const delay = Math.random() * 1000 + 500;
    logger.debug('ParallelProcessor', 'simulateTaskExecution', `模拟API延迟: ${delay.toFixed(0)}ms`);
    await new Promise(resolve => setTimeout(resolve, delay));

    const result = {
      model: modelConfig,
      content: `[${task.type}] 任务执行结果: ${task.content}`,
      timestamp: new Date().toISOString(),
      simulationDelay: `${delay.toFixed(0)}ms`
    };

    logger.debug('ParallelProcessor', 'simulateTaskExecution', `任务 ${task.id} 模拟执行完成`, result);
    return result;
  }
}

/**
 * 结果汇总器
 */
class ResultAggregator {
  constructor() {
    this.aggregatorPrompt = `你是一个专业的结果整合专家。请将多个子任务的执行结果整合成一个统一、连贯的回复。

整合要求：
1. 保持逻辑连贯性和完整性
2. 去除重复内容，合并相关信息
3. 按照用户原始请求的意图组织内容
4. 如果发现结果不完整或需要进一步处理，请明确指出

子任务结果：`;

    this.qualityCheckPrompt = `请评估以下整合结果的质量，并判断是否需要进一步处理：

评估标准：
1. 完整性：是否完全回答了用户的问题
2. 准确性：信息是否准确可靠
3. 连贯性：逻辑是否清晰连贯
4. 实用性：是否对用户有实际帮助

输出格式（严格JSON）：
{
  "quality_score": 1-10,
  "is_satisfactory": true/false,
  "issues": ["问题1", "问题2"],
  "suggestions": ["建议1", "建议2"],
  "needs_reprocessing": true/false
}

整合结果：`;
  }

  /**
   * 汇总任务结果
   */
  async aggregateResults(taskResults, originalPrompt, apiConfig) {
    console.log(`🔄 [结果汇总] 开始汇总${taskResults.size}个任务结果`);

    // 构建汇总提示
    let resultsText = "";
    taskResults.forEach((result, taskId) => {
      if (result.status === 'completed') {
        resultsText += `\n任务${taskId} (${result.task.type}):\n${result.result.content}\n`;
      } else {
        resultsText += `\n任务${taskId} (${result.task.type}): 执行失败 - ${result.error}\n`;
      }
    });

    const fullPrompt = this.aggregatorPrompt + resultsText + "\n\n用户原始请求：" + originalPrompt;

    try {
      // 选择汇总模型
      const aggregatorModel = this.selectAggregatorModel(fullPrompt.length);
      console.log(`🧠 [结果汇总] 使用${aggregatorModel.name}进行结果汇总`);

      // 这里需要实际调用模型API
      const aggregatedResult = await this.simulateAggregation(resultsText, originalPrompt);

      // 质量检查
      const qualityCheck = await this.checkQuality(aggregatedResult, originalPrompt);

      console.log(`✅ [结果汇总] 汇总完成，质量评分: ${qualityCheck.quality_score}/10`);

      return {
        content: aggregatedResult,
        quality: qualityCheck,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error(`❌ [结果汇总] 汇总失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 选择汇总模型
   */
  selectAggregatorModel(promptLength) {
    if (promptLength <= 100000) {
      return {
        model: "glm-4.5,ZhipuAI/GLM-4.5",
        name: "GLM-4.5"
      };
    } else {
      return {
        model: "gemini-pro,gemini-2.5-pro",
        name: "Gemini-Pro"
      };
    }
  }

  /**
   * 模拟结果汇总
   */
  async simulateAggregation(resultsText, originalPrompt) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return `基于多个专门模型的协作处理，以下是对您请求的综合回复：\n\n${resultsText}\n\n以上结果已经过专业模型的分工处理和智能整合。`;
  }

  /**
   * 质量检查
   */
  async checkQuality(aggregatedResult, originalPrompt) {
    await new Promise(resolve => setTimeout(resolve, 500));

    // 简单的质量评估逻辑
    const wordCount = aggregatedResult.length;
    const hasStructure = /\n\n/.test(aggregatedResult);
    const isRelevant = true; // 简化判断

    let qualityScore = 7;
    if (wordCount > 500) qualityScore += 1;
    if (hasStructure) qualityScore += 1;
    if (isRelevant) qualityScore += 1;

    return {
      quality_score: Math.min(qualityScore, 10),
      is_satisfactory: qualityScore >= 8,
      issues: qualityScore < 8 ? ["内容可能需要进一步完善"] : [],
      suggestions: ["可以考虑添加更多细节"],
      needs_reprocessing: qualityScore < 6
    };
  }
}

/**
 * 交互管理器
 */
class InteractionManager {
  constructor() {
    this.pendingInteractions = new Map();
    this.interactionQueue = [];
  }

  /**
   * 添加交互请求
   */
  addInteractionRequest(taskId, type, message, callback) {
    const interactionId = `interaction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const interaction = {
      id: interactionId,
      taskId,
      type, // 'confirmation', 'input', 'choice'
      message,
      callback,
      timestamp: new Date().toISOString(),
      status: 'pending'
    };

    this.pendingInteractions.set(interactionId, interaction);
    this.interactionQueue.push(interaction);

    console.log(`📝 [交互管理] 添加交互请求: ${type} - ${message}`);
    return interactionId;
  }

  /**
   * 处理用户响应
   */
  async handleUserResponse(interactionId, userResponse) {
    const interaction = this.pendingInteractions.get(interactionId);
    if (!interaction) {
      throw new Error(`交互请求 ${interactionId} 不存在`);
    }

    console.log(`✅ [交互管理] 处理用户响应: ${userResponse}`);

    interaction.status = 'completed';
    interaction.response = userResponse;

    // 执行回调
    if (interaction.callback) {
      await interaction.callback(userResponse);
    }

    // 从队列中移除
    this.pendingInteractions.delete(interactionId);
    this.interactionQueue = this.interactionQueue.filter(i => i.id !== interactionId);

    return interaction;
  }

  /**
   * 获取待处理的交互请求
   */
  getPendingInteractions() {
    return Array.from(this.pendingInteractions.values());
  }

  /**
   * 清空交互队列
   */
  clearInteractions() {
    this.pendingInteractions.clear();
    this.interactionQueue = [];
  }
}

/**
 * 主并行任务路由器
 */
class ParallelTaskRouter {
  constructor(apiConfig = {}) {
    this.decomposer = new TaskDecomposer();
    this.processor = new ParallelProcessor();
    this.aggregator = new ResultAggregator();
    this.interactionManager = new InteractionManager();
    this.apiConfig = apiConfig;

    // 统计信息
    this.stats = {
      totalRequests: 0,
      parallelRequests: 0,
      averageTaskCount: 0,
      averageProcessingTime: 0
    };
  }

  /**
   * 处理Claude请求的主入口
   */
  async processClaudeRequest(req, config) {
    const startTime = Date.now();
    this.stats.totalRequests++;

    console.log(`🚀 [并行路由] 开始处理Claude请求 #${this.stats.totalRequests}`);

    try {
      // 提取用户消息
      const userMessages = req.body.messages?.filter((m) => m.role === "user") || [];
      if (userMessages.length === 0) {
        throw new Error("没有找到用户消息");
      }

      const fullPrompt = userMessages
        .map((m) => {
          if (typeof m.content === "string") return m.content;
          if (Array.isArray(m.content)) {
            return m.content.map((item) => item.text || "").join(" ");
          }
          return "";
        })
        .join(" ");

      console.log(`📝 [并行路由] 分析用户输入: ${fullPrompt.length} 字符`);

      // 第一阶段：任务分解
      const decomposition = await this.decomposer.decomposeTasks(fullPrompt, this.apiConfig);

      // 更新统计信息
      this.stats.averageTaskCount = (this.stats.averageTaskCount * (this.stats.totalRequests - 1) + decomposition.tasks.length) / this.stats.totalRequests;

      if (decomposition.execution_strategy === "parallel") {
        this.stats.parallelRequests++;
      }

      // 第二阶段：并行处理
      const taskResults = await this.processor.executeTasks(decomposition, req, this.apiConfig);

      // 第三阶段：结果汇总
      const finalResult = await this.aggregator.aggregateResults(taskResults, fullPrompt, this.apiConfig);

      // 第四阶段：质量检查和重处理
      if (finalResult.quality.needs_reprocessing) {
        console.log(`🔄 [并行路由] 质量不满足要求，进行重处理`);
        return await this.reprocessResult(finalResult, fullPrompt, req);
      }

      const processingTime = Date.now() - startTime;
      this.stats.averageProcessingTime = (this.stats.averageProcessingTime * (this.stats.totalRequests - 1) + processingTime) / this.stats.totalRequests;

      console.log(`✅ [并行路由] 处理完成，耗时: ${processingTime}ms`);

      return {
        success: true,
        result: finalResult,
        decomposition,
        taskResults: Array.from(taskResults.values()),
        processingTime,
        stats: this.getStats()
      };

    } catch (error) {
      console.error(`❌ [并行路由] 处理失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
        fallbackModel: "glm-4.5,ZhipuAI/GLM-4.5" // 失败时的兜底模型
      };
    }
  }

  /**
   * 重处理结果
   */
  async reprocessResult(previousResult, originalPrompt, originalRequest) {
    console.log(`🔄 [重处理] 开始重新处理结果`);

    // 基于质量检查的建议重新分解任务
    const reprocessPrompt = `${originalPrompt}\n\n请特别注意以下问题：${previousResult.quality.issues.join(', ')}`;

    const newDecomposition = await this.decomposer.decomposeTasks(reprocessPrompt, this.apiConfig);
    const newTaskResults = await this.processor.executeTasks(newDecomposition, originalRequest, this.apiConfig);
    const newFinalResult = await this.aggregator.aggregateResults(newTaskResults, reprocessPrompt, this.apiConfig);

    return {
      success: true,
      result: newFinalResult,
      decomposition: newDecomposition,
      taskResults: Array.from(newTaskResults.values()),
      reprocessed: true
    };
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      ...this.stats,
      parallelRate: this.stats.totalRequests > 0 ? (this.stats.parallelRequests / this.stats.totalRequests * 100).toFixed(2) + '%' : '0%'
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      totalRequests: 0,
      parallelRequests: 0,
      averageTaskCount: 0,
      averageProcessingTime: 0
    };
  }
}

module.exports = {
  TaskDecomposer,
  ParallelProcessor,
  ResultAggregator,
  InteractionManager,
  ParallelTaskRouter
};
