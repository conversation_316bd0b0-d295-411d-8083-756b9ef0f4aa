{"LOG": true, "LOG_LEVEL": "warn", "CLAUDE_PATH": "", "HOST": "127.0.0.1", "PORT": 2526, "APIKEY": "lovejq4ever", "API_TIMEOUT_MS": "600000", "PROXY_URL": "", "transformers": [], "CUSTOM_ROUTER_PATH": "C:/Users/<USER>/.claude-code-router/velen_router.js", "Providers": [{"name": "gemini-flash", "api_base_url": "http://localhost:3001/proxy/gemini-flash/v1beta/models/", "api_key": "sk-Q9H6s-05qNj8sT9Vb-vM3FEdOR8y8M_uf4zEASZNqAoYPY_t", "models": ["gemini-2.5-flash"], "transformer": {"use": ["gemini"]}}, {"name": "gemini-pro", "api_base_url": "http://localhost:3001/proxy/gemini-pro/v1beta/models/", "api_key": "sk-7UTRrpB-tjTzKvnwe1V-WNHLnqUHWOWH0Hg6s4SPeOCZo4yd", "models": ["gemini-2.5-pro"], "transformer": {"use": ["gemini"]}}, {"name": "qwen3-coder", "api_base_url": "http://localhost:3001/proxy/modelscope-qwen3coder", "api_key": "sk-J9lP9cCgrCGh1JnZOpCyWoz4VrbUhA6315rCUNkgHJT2nE_O", "models": ["Qwen/Qwen3-Coder-480B-A35B-Instruct"]}, {"name": "qwen3-thinking", "api_base_url": "http://localhost:3001/proxy/modelscope-qwen3thinking", "api_key": "sk-ECQ6YD75QC4VQ5nM3WO_pTtag53mjUVedd4LwTF-WcyVC6pV", "models": ["Qwen/Qwen3-235B-A22B-Thinking-2507"]}, {"name": "qwen3-instruct", "api_base_url": "http://localhost:3001/proxy/modelscope-qwen3", "api_key": "sk-hmhCEyMx7Z4JBqzYZvXtZ5frMEAMRuXUmUxEi-_c7JKbnSgF", "models": ["Qwen/Qwen3-235B-A22B-Instruct-2507"]}, {"name": "glm-4.5", "api_base_url": "http://localhost:3001/proxy/modelscope-glm", "api_key": "sk-lpd32PGo-mRgQizQrnRhjED_aHgCRkb-CI3GHXpBy4-pju-d", "models": ["ZhipuAI/GLM-4.5"]}, {"name": "deepseek-v3.1", "api_base_url": "http://localhost:3001/proxy/iflow-deepseek-v", "api_key": "sk-vX3NxaqXNskWoApnwFiOOsZRypZv4YjdGol1bxZ0I5emaAan", "models": ["deepseek-v3.1"]}, {"name": "deepseek-r1", "api_base_url": "http://localhost:3001/proxy/iflow-deepseek-r", "api_key": "sk-RUashQXBD7Cf7wU65CHtw7zHHTf0opE1bnQTY2FDtq6qp6mr", "models": ["deepseek-r1"]}, {"name": "kimi-k2", "api_base_url": "http://localhost:3001/proxy/iflow-kimi-k2", "api_key": "sk-irqgMAiARKWvDE0ZGczIa18NHwlvc10CbCG0GQc5l9GomxtM", "models": ["kimi-k2"]}], "Router": {"default": "qwen3-coder,Qwen/Qwen3-Coder-480B-A35B-Instruct", "background": "gemini-flash,gemini-2.5-flash", "think": "qwen3-thinking,Qwen/Qwen3-235B-A22B-Thinking-2507", "longContext": "gemini-pro,gemini-2.5-pro", "longContextThreshold": 250000, "webSearch": "gemini-pro,gemini-2.5-pro"}, "StatusLine": {"enabled": true, "currentStyle": "default", "default": {"modules": [{"type": "workDir", "icon": "📚", "text": "{{workDirName}}", "color": "bright_blue"}, {"type": "model", "icon": "🤖", "text": "{{model}}", "color": "bright_yellow"}, {"type": "usage", "icon": "📊", "text": "{{inputTokens}} → {{outputTokens}}", "color": "bright_magenta"}]}, "powerline": {"modules": []}}}