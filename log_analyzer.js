/**
 * 日志分析工具
 * 分析并行任务路由器的执行日志，生成可读性强的报告
 */

const fs = require('fs');
const path = require('path');

class LogAnalyzer {
  constructor() {
    this.logs = [];
    this.components = new Set();
    this.steps = new Set();
    this.timeline = [];
  }

  /**
   * 解析日志数据
   */
  parseLogs(logData) {
    if (Array.isArray(logData)) {
      this.logs = logData;
    } else if (typeof logData === 'string') {
      try {
        this.logs = JSON.parse(logData);
      } catch (error) {
        console.error('解析日志数据失败:', error.message);
        return false;
      }
    }

    // 提取组件和步骤信息
    this.logs.forEach(log => {
      this.components.add(log.component);
      this.steps.add(log.step);
      this.timeline.push({
        timestamp: new Date(log.timestamp),
        component: log.component,
        step: log.step,
        level: log.level,
        message: log.message,
        data: log.data
      });
    });

    // 按时间排序
    this.timeline.sort((a, b) => a.timestamp - b.timestamp);
    return true;
  }

  /**
   * 生成执行流程报告
   */
  generateFlowReport() {
    console.log("\n🔍 执行流程分析报告");
    console.log("=" * 80);

    const componentColors = {
      'TaskDecomposer': '🧠',
      'ParallelProcessor': '⚡',
      'ResultAggregator': '📊',
      'InteractionManager': '💬',
      'ParallelTaskRouter': '🚀'
    };

    let currentComponent = null;
    let stepCounter = 1;

    this.timeline.forEach((entry, index) => {
      const icon = componentColors[entry.component] || '🔧';
      
      if (entry.component !== currentComponent) {
        if (currentComponent !== null) {
          console.log("");
        }
        console.log(`\n${icon} ${entry.component} 组件执行流程:`);
        console.log("-" * 50);
        currentComponent = entry.component;
        stepCounter = 1;
      }

      const timeStr = entry.timestamp.toISOString().substr(11, 12);
      const levelIcon = this.getLevelIcon(entry.level);
      
      console.log(`${stepCounter.toString().padStart(2)}. [${timeStr}] ${levelIcon} ${entry.step}`);
      console.log(`    ${entry.message}`);
      
      if (entry.data && Object.keys(entry.data).length > 0) {
        const dataStr = this.formatData(entry.data);
        console.log(`    📋 数据: ${dataStr}`);
      }
      
      stepCounter++;
    });

    console.log("\n" + "=" * 80);
  }

  /**
   * 生成性能分析报告
   */
  generatePerformanceReport() {
    console.log("\n⚡ 性能分析报告");
    console.log("=" * 80);

    // 按组件统计执行时间
    const componentStats = {};
    let startTime = null;
    let endTime = null;

    this.timeline.forEach(entry => {
      if (!startTime) startTime = entry.timestamp;
      endTime = entry.timestamp;

      if (!componentStats[entry.component]) {
        componentStats[entry.component] = {
          count: 0,
          firstSeen: entry.timestamp,
          lastSeen: entry.timestamp
        };
      }

      componentStats[entry.component].count++;
      componentStats[entry.component].lastSeen = entry.timestamp;
    });

    console.log(`📊 总执行时间: ${endTime - startTime}ms`);
    console.log(`📈 总日志条数: ${this.timeline.length}`);
    console.log(`🔧 涉及组件数: ${this.components.size}`);
    console.log(`📋 执行步骤数: ${this.steps.size}`);

    console.log("\n📊 各组件执行统计:");
    Object.entries(componentStats).forEach(([component, stats]) => {
      const duration = stats.lastSeen - stats.firstSeen;
      console.log(`   ${component}:`);
      console.log(`     日志条数: ${stats.count}`);
      console.log(`     执行时长: ${duration}ms`);
      console.log(`     开始时间: ${stats.firstSeen.toISOString().substr(11, 12)}`);
      console.log(`     结束时间: ${stats.lastSeen.toISOString().substr(11, 12)}`);
    });

    console.log("\n" + "=" * 80);
  }

  /**
   * 生成任务分解分析
   */
  generateTaskAnalysis() {
    console.log("\n🧠 任务分解分析");
    console.log("=" * 80);

    const decomposerLogs = this.timeline.filter(entry => 
      entry.component === 'TaskDecomposer'
    );

    if (decomposerLogs.length === 0) {
      console.log("❌ 未找到任务分解相关日志");
      return;
    }

    // 分析任务检测结果
    const detectionLog = decomposerLogs.find(log => 
      log.step === 'generateMockDecomposition' && 
      log.message === '任务分解完成' &&
      log.data && log.data.detectionResults
    );

    if (detectionLog) {
      console.log("🔍 任务类型检测结果:");
      detectionLog.data.detectionResults.forEach(result => {
        const status = result.matched ? '✅' : '❌';
        console.log(`   ${status} ${result.pattern}: ${result.matched ? `匹配 → ${result.task}` : '未匹配'}`);
      });

      console.log(`\n📊 分解统计:`);
      console.log(`   总任务数: ${detectionLog.data.totalTasks}`);
      console.log(`   执行策略: ${detectionLog.data.executionStrategy}`);
      console.log(`   复杂度评估: ${detectionLog.data.estimatedComplexity}/10`);
    }

    // 分析模型选择
    const modelSelectionLog = decomposerLogs.find(log => 
      log.step === 'selectDecomposerModel'
    );

    if (modelSelectionLog) {
      console.log(`\n🎯 分解器模型选择:`);
      console.log(`   选择模型: ${modelSelectionLog.data.name}`);
      console.log(`   选择原因: ${modelSelectionLog.data.reason}`);
      console.log(`   上下文限制: ${modelSelectionLog.data.contextLimit} 字符`);
    }

    console.log("\n" + "=" * 80);
  }

  /**
   * 生成错误分析报告
   */
  generateErrorReport() {
    console.log("\n❌ 错误分析报告");
    console.log("=" * 80);

    const errorLogs = this.timeline.filter(entry => entry.level === 'ERROR');
    const warnLogs = this.timeline.filter(entry => entry.level === 'WARN');

    if (errorLogs.length === 0 && warnLogs.length === 0) {
      console.log("✅ 未发现错误或警告");
      console.log("\n" + "=" * 80);
      return;
    }

    if (errorLogs.length > 0) {
      console.log(`🚨 发现 ${errorLogs.length} 个错误:`);
      errorLogs.forEach((error, index) => {
        console.log(`   ${index + 1}. [${error.component}] ${error.step}`);
        console.log(`      消息: ${error.message}`);
        if (error.data) {
          console.log(`      详情: ${this.formatData(error.data)}`);
        }
      });
    }

    if (warnLogs.length > 0) {
      console.log(`\n⚠️  发现 ${warnLogs.length} 个警告:`);
      warnLogs.forEach((warn, index) => {
        console.log(`   ${index + 1}. [${warn.component}] ${warn.step}`);
        console.log(`      消息: ${warn.message}`);
        if (warn.data) {
          console.log(`      详情: ${this.formatData(warn.data)}`);
        }
      });
    }

    console.log("\n" + "=" * 80);
  }

  /**
   * 获取日志级别图标
   */
  getLevelIcon(level) {
    const icons = {
      'DEBUG': '🔍',
      'INFO': 'ℹ️',
      'WARN': '⚠️',
      'ERROR': '❌'
    };
    return icons[level] || '📝';
  }

  /**
   * 格式化数据显示
   */
  formatData(data) {
    if (!data) return '';
    
    if (typeof data === 'string') return data;
    
    if (typeof data === 'object') {
      const keys = Object.keys(data);
      if (keys.length <= 3) {
        return JSON.stringify(data);
      } else {
        const preview = {};
        keys.slice(0, 3).forEach(key => {
          preview[key] = data[key];
        });
        return JSON.stringify(preview) + ` ... (${keys.length - 3} more)`;
      }
    }
    
    return String(data);
  }

  /**
   * 生成完整报告
   */
  generateFullReport() {
    this.generateFlowReport();
    this.generatePerformanceReport();
    this.generateTaskAnalysis();
    this.generateErrorReport();
  }
}

/**
 * 从文件加载日志并分析
 */
async function analyzeLogFile(filename) {
  try {
    if (!fs.existsSync(filename)) {
      console.log(`❌ 日志文件不存在: ${filename}`);
      return;
    }

    const logData = fs.readFileSync(filename, 'utf8');
    const analyzer = new LogAnalyzer();
    
    if (analyzer.parseLogs(logData)) {
      analyzer.generateFullReport();
    } else {
      console.log("❌ 解析日志文件失败");
    }
  } catch (error) {
    console.error(`❌ 分析日志文件时发生错误: ${error.message}`);
  }
}

module.exports = {
  LogAnalyzer,
  analyzeLogFile
};

// 如果直接运行此脚本
if (require.main === module) {
  const filename = process.argv[2] || 'parallel_router_logs.json';
  analyzeLogFile(filename);
}
