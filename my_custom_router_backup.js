/**
 * 简洁高效的Claude路由器 - 保持Claude最干净的状态
 * 核心原则：精确传递任务给更适合的模型，不干涉Claude Code的工具调用
 * 
 * 模型能力分析：
 * - GLM-4.5: 100%成功率，全能冠军 (128K上下文)
 * - Qwen3-235B-A22B-Instruct-2507: 90%成功率，通用能力强 (256K上下文)
 * - Qwen3-235B-A22B-Thinking-2507: 90%成功率，专门复杂推理 (256K上下文)
 * - Qwen3-Coder-480B: 90%成功率，代码生成SOTA (256K上下文)
 * - Gemini 2.5 Pro: 80%成功率，超长文本+多模态+网络搜索专家 (1M上下文)
 * - Gemini 2.5 Flash: 快速响应专家 (1M上下文)
 * - Kimi-K2: 80%成功率，多语言翻译专家 (200K上下文)
 * - DeepSeek-V3.1: 70%成功率，成本效率最优 (128K上下文)
 */

const fs = require('fs');
const path = require('path');

// 性能监控统计
const routingStats = {
  totalRequests: 0,
  modelUsage: new Map(),
  averageResponseTime: new Map()
};

/**
 * 记录路由决策
 */
function logRouting(model, description, startTime) {
  const duration = Date.now() - startTime;
  console.log(`[Clean Router] ${description} → ${model} (Request #${routingStats.totalRequests + 1})`);
  
  // 更新统计数据
  routingStats.totalRequests++;
  const providerKey = model.split(',')[0];
  routingStats.modelUsage.set(providerKey, (routingStats.modelUsage.get(providerKey) || 0) + 1);
  routingStats.averageResponseTime.set(providerKey, duration);
}

/**
 * 安全的日志记录
 */
function safeLog(message) {
  try {
    const logFile = path.join(__dirname, 'clean_router.log');
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - ${message}\n`;
    fs.appendFileSync(logFile, logEntry);
  } catch (error) {
    console.warn(`⚠️ [Clean Router] 日志写入失败: ${error.message}`);
  }
}

module.exports = async function cleanRouter(req, config) {
  const startTime = Date.now();
  const modelName = req.body?.model || '';

  // 安全日志记录
  safeLog(`路由处理: ${modelName}`);
  console.log(`🔥 [Clean Router] 处理模型: ${modelName}`);

  // === Claude模型拦截和路由 ===
  if (modelName && (modelName.includes('claude') || modelName.includes('anthropic'))) {
    console.log(`🎯 [Clean Router] 检测到Claude模型: ${modelName}`);

    // === 1. Claude Haiku → Gemini Flash (快速响应) ===
    if (modelName.includes('haiku')) {
      const model = "gemini-flash,gemini-2.5-flash";
      logRouting(model, "⚡ Claude-Haiku → Gemini Flash (快速响应)", startTime);
      return model;
    }

    // === 2. Claude Opus → GLM-4.5 (高质量处理) ===
    if (modelName.includes('opus')) {
      const model = "glm-4.5,ZhipuAI/GLM-4.5";
      logRouting(model, "👑 Claude-Opus → GLM-4.5 (高质量处理)", startTime);
      return model;
    }

    // === 3. Claude Sonnet → 智能模型决策层 ===
    if (modelName.includes('sonnet')) {
      console.log(`🧠 [Clean Router] Sonnet进入模型决策层: ${modelName}`);
      
      const userMessages = req.body.messages?.filter((m) => m.role === "user") || [];
      if (userMessages.length === 0) {
        const model = "glm-4.5,ZhipuAI/GLM-4.5";
        logRouting(model, "🔧 无用户消息，GLM-4.5兜底", startTime);
        return model;
      }

      // 提取用户输入内容
      const fullPrompt = userMessages
        .map((m) => {
          if (typeof m.content === "string") return m.content;
          if (Array.isArray(m.content)) {
            return m.content.map((item) => item.text || "").join(" ");
          }
          return "";
        })
        .join(" ");

      console.log(`📝 [Clean Router] 分析用户输入: ${fullPrompt.length} 字符`);

      // === 优先级任务检测 ===
      
      // Web Search请求检测
      if (Array.isArray(req.body.tools) &&
          req.body.tools.some((tool) => tool.type?.startsWith("web_search"))) {
        const model = "gemini-pro,gemini-2.5-pro";
        logRouting(model, "🔍 Web Search → Gemini Pro", startTime);
        return model;
      }

      // 图像处理任务检测
      const hasImages = req.body.messages?.some(m => 
        Array.isArray(m.content) && 
        m.content.some(item => item.type === 'image_url' || item.type === 'image')
      );
      
      if (hasImages || /图片|图像|照片|截图|image|photo|picture|screenshot/i.test(fullPrompt)) {
        const model = "gemini-pro,gemini-2.5-pro";
        logRouting(model, "👁️ 图像处理 → Gemini Pro", startTime);
        return model;
      }

      // === 专业化任务检测 ===

      // 代码生成任务
      const codeKeywords = [
        '写.*代码', '编程', '函数', '算法', '实现', '代码示例', '程序', '脚本', '开发',
        'write.*code', 'programming', 'function', 'algorithm', 'implement', 'code example',
        'python', 'javascript', 'java', 'c\\+\\+', 'react', 'vue', 'node\\.js'
      ];
      const codePattern = new RegExp(codeKeywords.join('|'), 'i');
      const hasCodeBlocks = /```[\s\S]*?```/.test(fullPrompt);
      
      if (codePattern.test(fullPrompt) || hasCodeBlocks) {
        const model = "qwen3-coder,Qwen/Qwen3-Coder-480B-A35B-Instruct";
        logRouting(model, "💻 代码生成 → Qwen3-Coder", startTime);
        return model;
      }

      // 数学推理任务
      const mathKeywords = [
        '证明', '推导', '数学', '计算', '公式', '定理', '几何', '代数', '微积分',
        'proof', 'derive', 'math', 'calculate', 'formula', 'theorem', 'reasoning'
      ];
      const mathPattern = new RegExp(mathKeywords.join('|'), 'i');
      const hasMathSymbols = /[∫∑∏√∂∇∆]|\$\$[\s\S]*?\$\$|\$[^$]+\$/.test(fullPrompt);
      
      if (mathPattern.test(fullPrompt) || hasMathSymbols) {
        const model = "qwen3-thinking,Qwen/Qwen3-235B-A22B-Thinking-2507";
        logRouting(model, "🧠 数学推理 → Qwen3-Thinking", startTime);
        return model;
      }

      // 翻译任务
      const translationKeywords = [
        '翻译', '译成', '转换成', '用.*语', '翻译成', '译为',
        'translate', 'translation', 'convert to', 'in.*language'
      ];
      const translationPattern = new RegExp(translationKeywords.join('|'), 'i');
      
      // 检测多语言内容
      const hasMultipleLanguages = [
        /[一-龯]/.test(fullPrompt), // 中文
        /[а-яё]/i.test(fullPrompt), // 俄文
        /[α-ωΑ-Ω]/.test(fullPrompt), // 希腊文
        /[ひらがなカタカナ]/.test(fullPrompt), // 日文
        /[가-힣]/.test(fullPrompt), // 韩文
      ].filter(Boolean).length > 1;

      if (translationPattern.test(fullPrompt) || hasMultipleLanguages) {
        const model = "kimi-k2,kimi-k2";
        logRouting(model, "🌍 翻译任务 → Kimi-K2", startTime);
        return model;
      }

      // 创意写作任务
      const creativeKeywords = [
        '写.*故事', '创作', '小说', '诗歌', '文案', '剧本', '散文', '创意',
        'write.*story', 'creative', 'novel', 'poem', 'poetry', 'script'
      ];
      const creativePattern = new RegExp(creativeKeywords.join('|'), 'i');

      if (creativePattern.test(fullPrompt)) {
        const model = "glm-4.5,ZhipuAI/GLM-4.5";
        logRouting(model, "🎨 创意写作 → GLM-4.5", startTime);
        return model;
      }

      // === 阶梯式上下文长度处理 ===
      
      // 超长文本 (1M上下文)
      if (fullPrompt.length > 250000) {
        const model = "gemini-pro,gemini-2.5-pro";
        logRouting(model, "📖 超长文本 → Gemini Pro", startTime);
        return model;
      }

      // 长文本 (256K上下文)
      if (fullPrompt.length > 100000) {
        const model = "qwen3-instruct,Qwen/Qwen3-235B-A22B-Instruct-2507";
        logRouting(model, "📚 长文本 → Qwen3-Instruct", startTime);
        return model;
      }

      // 成本效率优化
      const costEfficiencyKeywords = [
        '简单', '基础', '入门', '概述', '简介', '什么是', '如何',
        'simple', 'basic', 'introduction', 'overview', 'what is', 'how to'
      ];
      const costEfficiencyPattern = new RegExp(costEfficiencyKeywords.join('|'), 'i');

      if (costEfficiencyPattern.test(fullPrompt) && fullPrompt.length < 2000) {
        const model = "deepseek-v3.1,deepseek-v3.1";
        logRouting(model, "💰 成本优化 → DeepSeek-V3.1", startTime);
        return model;
      }

      // === 默认兜底：GLM-4.5全能处理 ===
      const model = "glm-4.5,ZhipuAI/GLM-4.5";
      logRouting(model, "🏆 通用任务 → GLM-4.5", startTime);
      return model;
    }
  }

  // === 非Claude请求，进入默认路由 ===
  console.log(`📋 [Clean Router] 非Claude请求，使用默认路由: ${modelName}`);
  return null;
};

/**
 * 获取路由统计信息
 */
function getRoutingStats() {
  return {
    totalRequests: routingStats.totalRequests,
    modelUsage: Object.fromEntries(routingStats.modelUsage),
    averageResponseTime: Object.fromEntries(routingStats.averageResponseTime)
  };
}

module.exports.getStats = getRoutingStats;
